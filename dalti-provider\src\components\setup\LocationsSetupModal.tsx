import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { ErrorDisplay } from '../error';
import { useLocations, useDeleteLocation } from '../../hooks/useLocations';
import LocationForm from '../locations/LocationForm';
import LocationCard from '../locations/LocationCard';
import { Location, LocationFilters } from '../../types';
import { useTranslation, useCommonTranslation } from '../../hooks/useTranslation';

interface LocationsSetupModalProps {
  onComplete: () => void;
  onCancel: () => void;
}

export default function LocationsSetupModal({ onComplete, onCancel }: LocationsSetupModalProps) {
  const { t } = useTranslation('forms');
  const { t: tCommon } = useCommonTranslation();
  const [showLocationForm, setShowLocationForm] = useState(false);
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  const [filters, setFilters] = useState<LocationFilters>({});

  const { data: locations, isLoading, error } = useLocations(filters);
  const deleteLocationMutation = useDeleteLocation();

  const handleCreateLocation = () => {
    setEditingLocation(null);
    setShowLocationForm(true);
  };

  const handleEditLocation = (location: Location) => {
    setEditingLocation(location);
    setShowLocationForm(true);
  };

  const handleDeleteLocation = async (locationId: number) => {
    if (window.confirm(t('validation.confirmDeleteLocation'))) {
      try {
        await deleteLocationMutation.mutateAsync(locationId);
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  const handleLocationFormClose = () => {
    setShowLocationForm(false);
    setEditingLocation(null);
  };

  const handleContinue = () => {
    if (locations && locations.length > 0) {
      onComplete();
    } else {
      alert(t('validation.atLeastOneLocation'));
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p className="ml-3 text-gray-600 dark:text-gray-400">{tCommon('messages.loading')}</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-6">
        <ErrorDisplay
          error={error}
          title="Failed to load locations"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {t('labels.addYourBusinessLocations')}
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          {t('placeholders.locationsHelpText')}
        </p>
      </div>

      {/* Location Form */}
      {showLocationForm && (
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-gray-50 dark:bg-gray-800/50">
          <LocationForm
            location={editingLocation}
            onSuccess={handleLocationFormClose}
            onCancel={handleLocationFormClose}
          />
        </div>
      )}

      {/* Add Location Button */}
      {!showLocationForm && (
        <div className="flex justify-center">
          <Button
            onClick={handleCreateLocation}
            className="flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            {t('labels.addLocation')}
          </Button>
        </div>
      )}

      {/* Locations List */}
      {locations && locations.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            {t('labels.yourLocations')} ({locations.length})
          </h4>
          <div className="grid gap-4 md:grid-cols-2">
            {locations.map((location) => (
              <LocationCard
                key={location.id}
                location={location}
                onEdit={() => handleEditLocation(location)}
                onDelete={() => handleDeleteLocation(location.id)}
                onManageHours={() => {}} // Not needed in setup modal
                isDeleting={deleteLocationMutation.isPending}
              />
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {locations && locations.length === 0 && !showLocationForm && (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {tCommon('messages.noDataFound')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('placeholders.addLocationHelpText')}
          </p>
          <Button onClick={handleCreateLocation}>
            {t('labels.addYourFirstLocation')}
          </Button>
        </div>
      )}

      {/* Progress Indicator */}
      {locations && locations.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 dark:bg-green-900/20 dark:border-green-800">
          <div className="flex items-center">
            <span className="text-green-500 mr-2 text-xl">✅</span>
            <div>
              <h4 className="text-green-800 dark:text-green-200 font-medium">
                {t('placeholders.locationsConfiguredText')
                  .replace('{count}', locations.length.toString())
                  .replace('{plural}', locations.length !== 1 ? 's' : '')}
              </h4>
              <p className="text-green-700 dark:text-green-300 text-sm">
                {t('placeholders.addMoreLocationsText')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          {t('buttons.cancel')}
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!locations || locations.length === 0}
        >
          {tCommon('actions.continue')}
        </Button>
      </div>
    </div>
  );
}
