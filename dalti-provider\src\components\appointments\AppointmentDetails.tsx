import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { Appointment, AppointmentStatus } from '../../types';
import AppointmentStatusTracker from './AppointmentStatusTracker';
import {
  useRescheduleAppointment,
  useUpdateAppointmentStatus,
} from '../../hooks/useAppointments';
import { formatLocalDateTimeWithLocale, formatLocalTimeWithLocale, formatLocalDateWithLocale } from '../../utils/timezone';
import { useCommonTranslation, useManagementTranslation } from '../../hooks/useTranslation';
import { useLanguage } from '../../context/LanguageContext';
import { useCurrency } from '../../context/CurrencyContext';
import { useNavigate } from 'react-router';

interface AppointmentDetailsProps {
  appointment: Appointment;
  onClose: () => void;
  onEdit?: () => void; // Made optional since we're removing edit functionality
}



export default function AppointmentDetails({
  appointment,
  onClose,
  onEdit: _onEdit // Renamed to indicate it's intentionally unused
}: AppointmentDetailsProps) {
  const navigate = useNavigate();
  const [showCancelForm, setShowCancelForm] = useState(false);
  const [showCompleteForm, setShowCompleteForm] = useState(false);
  const [showRescheduleForm, setShowRescheduleForm] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [completionNotes, setCompletionNotes] = useState('');
  const [newDateTime, setNewDateTime] = useState('');


  const rescheduleMutation = useRescheduleAppointment();
  const updateStatusMutation = useUpdateAppointmentStatus();
  const { t: tCommon, currentLanguage } = useCommonTranslation();
  const { t } = useManagementTranslation();
  const { isRTL } = useLanguage();
  const { formatPrice } = useCurrency();

  const formatDateTime = (dateTime: string) => {
    return {
      date: formatLocalDateWithLocale(dateTime, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }, currentLanguage),
      time: formatLocalTimeWithLocale(dateTime, {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      }, currentLanguage),
    };
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'InProgress':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'completed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'canceled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'noshow':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      // Legacy status support
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'no-show':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      default:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
    }
  };

  const handleConfirm = async () => {
    try {
      await updateStatusMutation.mutateAsync({
        id: appointment.id,
        status: {
          status: 'confirmed',
          notes: 'Appointment confirmed by provider'
        }
      });
      onClose();
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleCancel = async () => {
    try {
      await updateStatusMutation.mutateAsync({
        id: appointment.id,
        status: {
          status: 'canceled',
          notes: cancelReason || 'Appointment cancelled'
        }
      });
      onClose();
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleComplete = async () => {
    try {
      await updateStatusMutation.mutateAsync({
        id: appointment.id,
        status: {
          status: 'completed',
          notes: completionNotes || 'Appointment completed'
        }
      });
      onClose();
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleReschedule = async () => {
    try {
      await rescheduleMutation.mutateAsync({
        id: appointment.id,
        newDateTime,
      });
      onClose();
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleStartAppointment = async () => {
    try {
      await updateStatusMutation.mutateAsync({
        id: appointment.id,
        status: {
          status: 'InProgress',
          notes: t('appointments.details.appointmentStarted', 'Appointment started')
        }
      });
      onClose();
      // Navigate to service session page
      navigate(`/service-session/${appointment.id}`);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleNoShow = async () => {
    try {
      await updateStatusMutation.mutateAsync({
        id: appointment.id,
        status: {
          status: 'noshow',
          notes: 'Customer did not show up for appointment'
        }
      });
      onClose();
    } catch (error) {
      // Error handled by mutation
    }
  };



  const { date, time } = formatDateTime(appointment.expectedAppointmentStartTime);
  const isLoading = rescheduleMutation.isPending ||
                   updateStatusMutation.isPending;



  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('appointments.details.title', 'Appointment Details')}
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {t('appointments.details.subtitle', 'View and manage appointment information')}
          </p>
          <div className="mt-3 space-y-3">
            <div>
              <span
                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                  appointment.status
                )}`}
              >
                {tCommon(`status.${appointment.status}`)}
              </span>
            </div>




          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="space-y-6">
        {/* Customer Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            {t('appointments.details.customerInformation', 'Customer Information')}
          </h3>
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-2">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm me-3">
                {appointment.customer?.firstName?.charAt(0)}{appointment.customer?.lastName?.charAt(0)}
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {appointment.customer?.firstName} {appointment.customer?.lastName}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {appointment.customer?.email}
                </p>
              </div>
            </div>
            {appointment.customer?.phone && (
              <p className="text-sm text-gray-600 dark:text-gray-400 ms-13">
                {tCommon('common.phone')}: {appointment.customer.phone}
              </p>
            )}
          </div>
        </div>

        {/* Service Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            {t('appointments.details.serviceDetails', 'Service Details')}
          </h3>
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">{t('appointments.details.service', 'Service')}:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {appointment.service?.title}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">{tCommon('common.duration')}:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {appointment.service?.duration} {t('appointments.details.minutes', 'minutes')}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">{tCommon('common.price')}:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {formatPrice(appointment.service?.price || 0)}
              </span>
            </div>
          </div>
        </div>

        {/* Date & Time */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            {t('appointments.details.schedule', 'Schedule')}
          </h3>
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">{tCommon('common.date')}:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {date}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">{tCommon('common.time')}:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {time}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">{t('appointments.details.location', 'Location')}:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {appointment.place?.name}
              </span>
            </div>
          </div>
        </div>

        {/* Notes */}
        {appointment.notes && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              {tCommon('common.notes')}
            </h3>
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <p className="text-gray-700 dark:text-gray-300">
                {appointment.notes}
              </p>
            </div>
          </div>
        )}

        {/* Status Timeline */}
        <div>
          <AppointmentStatusTracker appointment={appointment} />
        </div>

        {/* Action Forms */}
        {showCancelForm && (
          <div className="border border-red-200 dark:border-red-800 rounded-lg p-4">
            <h4 className="font-medium text-red-800 dark:text-red-400 mb-3">
              {t('appointments.details.cancelAppointment', 'Cancel Appointment')}
            </h4>
            <textarea
              value={cancelReason}
              onChange={(e) => setCancelReason(e.target.value)}
              placeholder={t('appointments.details.cancelReasonPlaceholder', 'Reason for cancellation (optional)')}
              rows={3}
              className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
            />
            <div className="flex justify-end gap-2 mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCancelForm(false)}
              >
                {tCommon('actions.cancel')}
              </Button>
              <Button
                size="sm"
                onClick={handleCancel}
                disabled={isLoading}
                className="bg-red-600 hover:bg-red-700"
              >
                {updateStatusMutation.isPending ? t('appointments.details.cancelling', 'Cancelling...') : t('appointments.details.confirmCancel', 'Confirm Cancel')}
              </Button>
            </div>
          </div>
        )}

        {showCompleteForm && (
          <div className="border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 className="font-medium text-green-800 dark:text-green-400 mb-3">
              {t('appointments.details.completeAppointment', 'Complete Appointment')}
            </h4>
            <textarea
              value={completionNotes}
              onChange={(e) => setCompletionNotes(e.target.value)}
              placeholder={t('appointments.details.completionNotesPlaceholder', 'Completion notes (optional)')}
              rows={3}
              className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
            />
            <div className="flex justify-end gap-2 mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCompleteForm(false)}
              >
                {tCommon('actions.cancel')}
              </Button>
              <Button
                size="sm"
                onClick={handleComplete}
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700"
              >
                {updateStatusMutation.isPending ? t('appointments.details.completing', 'Completing...') : t('appointments.details.markComplete', 'Mark Complete')}
              </Button>
            </div>
          </div>
        )}

        {showRescheduleForm && (
          <div className="border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 dark:text-blue-400 mb-3">
              {t('appointments.details.rescheduleAppointment', 'Reschedule Appointment')}
            </h4>
            <div className={isRTL ? 'rtl-datetime-input' : ''}>
              <input
                type="datetime-local"
                value={newDateTime}
                onChange={(e) => setNewDateTime(e.target.value)}
                className={`w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800 ${isRTL ? 'rtl' : ''}`}
                dir="ltr"
              />
            </div>
            <div className="flex justify-end gap-2 mt-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowRescheduleForm(false)}
              >
                {tCommon('actions.cancel')}
              </Button>
              <Button
                size="sm"
                onClick={handleReschedule}
                disabled={isLoading || !newDateTime}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {rescheduleMutation.isPending ? t('appointments.details.rescheduling', 'Rescheduling...') : t('appointments.details.reschedule', 'Reschedule')}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700 mt-6">
        <div className="flex gap-2">
          
          {appointment.status === 'pending' && (
            <Button
              size="sm"
              onClick={handleConfirm}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {updateStatusMutation.isPending ? t('appointments.details.confirming', 'Confirming...') : tCommon('actions.confirm')}
            </Button>
          )}

          {appointment.status === 'confirmed' && (
            <Button
              size="sm"
              onClick={handleStartAppointment}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {updateStatusMutation.isPending ? t('appointments.details.starting', 'Starting...') : t('appointments.details.start', 'Start')}
            </Button>
          )}
          
          {(appointment.status === 'pending' || appointment.status === 'confirmed' || appointment.status === 'InProgress') && (
            <>
              {(appointment.status === 'pending' || appointment.status === 'confirmed') && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowRescheduleForm(true)}
                >
                  {t('appointments.details.reschedule', 'Reschedule')}
                </Button>
              )}

              {(appointment.status === 'confirmed' || appointment.status === 'InProgress') && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCompleteForm(true)}
                  className="text-green-600 border-green-300 hover:bg-green-50 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/20"
                >
                  {t('appointments.details.complete', 'Complete')}
                </Button>
              )}

              {appointment.status !== 'completed' && appointment.status !== 'canceled' && appointment.status !== 'noshow' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCancelForm(true)}
                  className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                >
                  {tCommon('actions.cancel')}
                </Button>
              )}

              {(appointment.status === 'confirmed' || appointment.status === 'InProgress') && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNoShow}
                  disabled={isLoading}
                  className="text-orange-600 border-orange-300 hover:bg-orange-50 dark:text-orange-400 dark:border-orange-800 dark:hover:bg-orange-900/20"
                >
                  {updateStatusMutation.isPending ? t('appointments.details.marking', 'Marking...') : t('appointments.details.noShow', 'No Show')}
                </Button>
              )}
            </>
          )}
        </div>
        
        <Button
          variant="outline"
          onClick={onClose}
        >
          {tCommon('actions.close')}
        </Button>
        </div>
      </div>
    </div>
  );
}
