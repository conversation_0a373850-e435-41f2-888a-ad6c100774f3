import { useQuery } from '@tanstack/react-query';
import { DashboardService } from '../services/dashboard.service';

/**
 * Hook for fetching dashboard metrics
 */
export const useDashboardMetrics = () => {
  return useQuery({
    queryKey: ['dashboard', 'metrics'],
    queryFn: () => DashboardService.getDashboardMetrics(),
    staleTime: 30 * 1000, // 30 seconds - more aggressive for dashboard
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
    refetchIntervalInBackground: true, // Continue refreshing when tab is not active
    refetchOnWindowFocus: true, // Refetch when window regains focus
    refetchOnMount: true, // Always refetch when component mounts
  });
};

/**
 * Hook for fetching today's appointments
 */
export const useTodayAppointments = () => {
  return useQuery({
    queryKey: ['dashboard', 'today-appointments'],
    queryFn: () => DashboardService.getTodayAppointments(),
    staleTime: 30 * 1000, // 30 seconds - more aggressive for critical data
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
    refetchIntervalInBackground: true, // Continue refreshing when tab is not active
    refetchOnWindowFocus: true, // Refetch when window regains focus
    refetchOnMount: true, // Always refetch when component mounts
  });
};

/**
 * Hook for fetching revenue chart data
 */
export const useRevenueChart = (period: 'week' | 'month' = 'week') => {
  return useQuery({
    queryKey: ['dashboard', 'revenue-chart', period],
    queryFn: () => DashboardService.getRevenueChart(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};
