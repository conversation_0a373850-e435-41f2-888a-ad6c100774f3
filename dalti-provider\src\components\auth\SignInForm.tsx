import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ChevronLeftIcon, ChevronRightIcon, EyeCloseIcon, EyeIcon } from "../../icons";
import Label from "../form/Label";
import Input from "../form/input/InputField";
import Checkbox from "../form/input/Checkbox";
import Button from "../ui/button/Button";
import { ErrorDisplay } from "../error";
import { useAuth } from "../../context/AuthContext";
import { LoginRequest } from "../../types";
import { useAuthTranslation, useCommonTranslation } from "../../hooks/useTranslation";

// Type for form data
type LoginFormData = {
  identifier: string;
  password: string;
  rememberMe?: boolean;
};

export default function SignInForm() {
  const navigate = useNavigate();
  const { login, isLoading, error, clearError } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const { t } = useAuthTranslation();
  const { currentLanguage, t: tCommon } = useCommonTranslation();

  // Validation schema using translations
  const loginSchema = z.object({
    identifier: z.string().min(1, t('validation.emailRequired')),
    password: z.string().min(1, t('validation.passwordRequired')),
    rememberMe: z.boolean().default(false),
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      rememberMe: false,
    },
  });

  // Clear errors when user starts typing again
  const identifierValue = watch("identifier");
  const passwordValue = watch("password");

  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [identifierValue, passwordValue, clearError]);

  const onSubmit = async (data: LoginFormData) => {
    if (import.meta.env.DEV) {
      console.log('📝 Login form submitted with:', { identifier: data.identifier });
      console.trace('Form submission stack trace');
    }

    try {
      await login({
        identifier: data.identifier,
        password: data.password,
      });

      if (import.meta.env.DEV) {
        console.log('✅ Login successful, navigation commented out');
      }

      // Only navigate if login was successful
      // navigate("/", { replace: true });
    } catch (error) {
      // Error is handled by the auth context
      // Stay on login page - don't navigate
      if (import.meta.env.DEV) {
        console.log('❌ Login failed:', error);
        console.trace('Login error stack trace');
      }
    }
  };

  const isFormLoading = isLoading || isSubmitting;
  return (
    <div className="flex flex-col flex-1">
      {/* <div className="w-full max-w-md pt-10 mx-auto">
        <Link
          to="/"
          className="inline-flex items-center text-sm text-gray-500 transition-colors hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          {currentLanguage === 'ar' ? (
            <ChevronRightIcon className="size-5" />
          ) : (
            <ChevronLeftIcon className="size-5" />
          )}
          {tCommon('actions.backToDashboard')}
        </Link>
      </div> */}
      <div className="flex flex-col justify-center flex-1 w-full max-w-md mx-auto">
        <div>
          <div className="mb-5 sm:mb-8">
            <h1 className="mb-2 font-semibold text-gray-800 text-title-sm dark:text-white/90 sm:text-title-md">
              {t('signIn.title')}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {t('signIn.subtitle')}
            </p>
          </div>
          <div>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-6">
                <div>
                  <Label>
                    {t('signIn.email')} <span className="text-error-500">*</span>
                  </Label>
                  <Input
                    {...register("identifier")}
                    placeholder={t('signIn.emailPlaceholder')}
                    disabled={isFormLoading}
                  />
                  {errors.identifier && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.identifier.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label>
                    {t('signIn.password')} <span className="text-error-500">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      {...register("password")}
                      type={showPassword ? "text" : "password"}
                      placeholder={t('signIn.passwordPlaceholder')}
                      disabled={isFormLoading}
                      className={currentLanguage === 'ar' ? 'pl-10' : 'pr-10'}
                    />
                    <span
                      onClick={() => setShowPassword(!showPassword)}
                      className={`absolute z-30 -translate-y-1/2 cursor-pointer top-1/2 ${
                        currentLanguage === 'ar' ? 'left-4' : 'right-4'
                      }`}
                    >
                      {showPassword ? (
                        <EyeIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                      ) : (
                        <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                      )}
                    </span>
                  </div>
                  {errors.password && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.password.message}
                    </p>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={watch("rememberMe") || false}
                      onChange={(checked) => setValue("rememberMe", checked)}
                      disabled={isFormLoading}
                    />
                    <span className="block font-normal text-gray-700 text-theme-sm dark:text-gray-400">
                      {t('signIn.rememberMe')}
                    </span>
                  </div>
                  <Link
                    to="/reset-password"
                    className="text-sm text-brand-500 hover:text-brand-600 dark:text-brand-400"
                  >
                    {t('signIn.forgotPassword')}
                  </Link>
                </div>

                {error && (
                  <ErrorDisplay
                    error={error}
                    variant="banner"
                    size="sm"
                  />
                )}

                <div>
                  <Button
                    type="submit"
                    className="w-full"
                    size="sm"
                    disabled={isFormLoading}
                  >
                    {isFormLoading ? t('signIn.signingIn', 'Signing in...') : t('signIn.signInButton')}
                  </Button>
                </div>
              </div>
            </form>

            <div className="mt-5">
              <p className="text-sm font-normal text-center text-gray-700 dark:text-gray-400 sm:text-start">
                {t('signIn.noAccount')} {""}
                <Link
                  to="/signup"
                  className="text-brand-500 hover:text-brand-600 dark:text-brand-400"
                >
                  {t('signIn.signUpLink')}
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
