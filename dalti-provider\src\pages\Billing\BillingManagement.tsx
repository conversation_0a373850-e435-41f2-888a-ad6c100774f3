import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { ErrorDisplay } from "../../components/error";
import { 
  useSubscriptionStatus, 
  useUsageStatistics,
  useUsageForPeriod
} from "../../hooks/useSubscription";
import {
  UsageStatistics,
  CustomerPortalCard
} from "../../components/subscription";
import { UsagePeriod } from "../../types";

export default function BillingManagement() {
  const [selectedPeriod, setSelectedPeriod] = useState<UsagePeriod>('month');
  const [exportLoading, setExportLoading] = useState(false);

  const { data: statusData, isLoading: statusLoading, error: statusError } = useSubscriptionStatus();
  const { data: usageData, isLoading: usageLoading } = useUsageForPeriod(selectedPeriod);

  const handleExportUsage = async () => {
    setExportLoading(true);
    try {
      // Simulate export functionality
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create CSV content
      const csvContent = generateUsageCSV();
      
      // Download CSV
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `usage-report-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setExportLoading(false);
    }
  };

  const generateUsageCSV = () => {
    const headers = ['Metric', 'Current', 'Limit', 'Usage %', 'Period'];
    const data = [];
    
    if (usageData?.data) {
      const usage = usageData.data;
      data.push([
        'Credits',
        usage.current.credits.toString(),
        usage.limits.credits.toString(),
        ((usage.current.credits / usage.limits.credits) * 100).toFixed(2) + '%',
        selectedPeriod
      ]);
      data.push([
        'Queues',
        usage.current.queues.toString(),
        usage.limits.queues.toString(),
        ((usage.current.queues / usage.limits.queues) * 100).toFixed(2) + '%',
        selectedPeriod
      ]);
    }
    
    return [headers, ...data].map(row => row.join(',')).join('\n');
  };

  const getUsageAlerts = () => {
    if (!usageData?.data) return [];
    
    const alerts = [];
    const usage = usageData.data;
    
    const creditsPercentage = (usage?.current?.credits / usage?.limits?.credits) * 100;
    const queuesPercentage = (usage?.current?.queues / usage?.limits?.queues) * 100;
    
    if (creditsPercentage >= 90) {
      alerts.push({
        type: 'error',
        title: 'Credits Almost Depleted',
        message: `You've used ${creditsPercentage.toFixed(1)}% of your credits. Consider upgrading your plan.`,
        action: 'Upgrade Plan'
      });
    } else if (creditsPercentage >= 75) {
      alerts.push({
        type: 'warning',
        title: 'High Credit Usage',
        message: `You've used ${creditsPercentage.toFixed(1)}% of your credits this ${selectedPeriod}.`,
        action: 'View Plans'
      });
    }
    
    if (queuesPercentage >= 90) {
      alerts.push({
        type: 'error',
        title: 'Queue Limit Reached',
        message: `You're using ${usage.current.queues} of ${usage.limits.queues} available queues.`,
        action: 'Upgrade Plan'
      });
    }
    
    return alerts;
  };

  if (statusLoading || usageLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (statusError) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={statusError}
          title="Failed to load billing data"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  const subscription = statusData?.data?.subscription;
  const user = statusData?.data?.user;
  const alerts = getUsageAlerts();

  return (
    <>
      <PageMeta
        title="Billing & Usage | Provider Dashboard"
        description="View your billing information, usage statistics, and manage your subscription"
      />
      <PageBreadcrumb pageTitle="Billing & Usage" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Billing & Usage
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Monitor your usage, view billing information, and export reports
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleExportUsage}
              variant="outline"
              size="sm"
              disabled={exportLoading}
            >
              {exportLoading ? 'Exporting...' : 'Export Usage'}
            </Button>
          </div>
        </div>

        {/* Usage Alerts */}
        {alerts.length > 0 && (
          <div className="space-y-3">
            {alerts.map((alert, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${
                  alert.type === 'error'
                    ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                    : 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg
                      className={`w-5 h-5 ${
                        alert.type === 'error' ? 'text-red-500' : 'text-yellow-500'
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
                      />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4
                      className={`text-sm font-medium ${
                        alert.type === 'error'
                          ? 'text-red-800 dark:text-red-200'
                          : 'text-yellow-800 dark:text-yellow-200'
                      }`}
                    >
                      {alert.title}
                    </h4>
                    <p
                      className={`text-sm mt-1 ${
                        alert.type === 'error'
                          ? 'text-red-700 dark:text-red-300'
                          : 'text-yellow-700 dark:text-yellow-300'
                      }`}
                    >
                      {alert.message}
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-xs"
                    onClick={() => window.location.href = '/subscription'}
                  >
                    {alert.action}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Current Subscription Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Current Subscription
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Plan</p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {subscription?.planName || 'No Plan'}
                  </p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</p>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    subscription?.isActive
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                  }`}>
                    {subscription?.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                
                {user?.datePaid && (
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Payment</p>
                    <p className="text-sm text-gray-900 dark:text-white">
                      {new Date(user.datePaid).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div>
            <CustomerPortalCard />
          </div>
        </div>

        {/* Usage Statistics */}
        <div>
          <UsageStatistics 
            showPeriodSelector={true}
            defaultPeriod={selectedPeriod}
          />
        </div>

        {/* Usage History Summary */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Usage Summary
          </h3>
          
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Detailed Usage History
            </h4>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              Historical usage data and billing history will be available here once you have more activity.
            </p>
            <Button
              onClick={handleExportUsage}
              variant="outline"
              size="sm"
              disabled={exportLoading}
            >
              {exportLoading ? 'Generating...' : 'Export Current Usage'}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
