import React, { useState } from 'react';
import { useLanguage } from '../context/LanguageContext';
import { useUpdatePreferredLanguage } from '../hooks/useUser';
import { UserService } from '../services/user.service';
import { LanguageCode } from '../context/LanguageContext';
import PageMeta from '../components/common/PageMeta';

const TestLanguageUpdate: React.FC = () => {
  const { currentLanguage, changeLanguage, isChangingLanguage } = useLanguage();
  const updateLanguageMutation = useUpdatePreferredLanguage();
  const [testResult, setTestResult] = useState<string>('');
  const [isTestingDirect, setIsTestingDirect] = useState(false);

  const handleLanguageChange = async (languageCode: LanguageCode) => {
    try {
      await changeLanguage(languageCode);
      setTestResult(`✅ Language changed to ${languageCode} successfully`);
    } catch (error) {
      setTestResult(`❌ Failed to change language: ${error}`);
    }
  };

  const handleDirectApiTest = async (languageCode: LanguageCode) => {
    setIsTestingDirect(true);
    setTestResult('');
    
    try {
      await UserService.updatePreferredLanguage(languageCode);
      setTestResult(`✅ Direct API call successful for ${languageCode}`);
    } catch (error: any) {
      setTestResult(`❌ Direct API call failed: ${error.message}`);
    } finally {
      setIsTestingDirect(false);
    }
  };

  const handleMutationTest = async (languageCode: LanguageCode) => {
    try {
      await updateLanguageMutation.mutateAsync(languageCode);
      setTestResult(`✅ Mutation hook successful for ${languageCode}`);
    } catch (error: any) {
      setTestResult(`❌ Mutation hook failed: ${error.message}`);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <PageMeta
        title="Language Update Test - Dalti Provider Dashboard"
        description="Testing preferred language update functionality"
      />
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Language Update Test Page
        </h1>
        
        <div className="space-y-6">
          {/* Current Language Status */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
              Current Status
            </h2>
            <p className="text-blue-800 dark:text-blue-200">
              Current Language: <span className="font-mono font-bold">{currentLanguage}</span>
            </p>
            <p className="text-blue-800 dark:text-blue-200">
              Is Changing: <span className="font-mono">{isChangingLanguage ? 'Yes' : 'No'}</span>
            </p>
          </div>

          {/* Test Results */}
          {testResult && (
            <div className={`p-4 rounded-lg ${
              testResult.startsWith('✅') 
                ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200'
                : 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200'
            }`}>
              <h3 className="font-semibold mb-1">Test Result:</h3>
              <p className="font-mono text-sm">{testResult}</p>
            </div>
          )}

          {/* Test 1: Language Context Change (includes backend sync) */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Test 1: Language Context Change (with Backend Sync)
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              This tests the full language change flow including backend synchronization.
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => handleLanguageChange('en')}
                disabled={isChangingLanguage}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Switch to English
              </button>
              <button
                onClick={() => handleLanguageChange('fr')}
                disabled={isChangingLanguage}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Switch to French
              </button>
              <button
                onClick={() => handleLanguageChange('ar')}
                disabled={isChangingLanguage}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Switch to Arabic
              </button>
            </div>
          </div>

          {/* Test 2: Direct API Call */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Test 2: Direct API Call
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              This tests the UserService.updatePreferredLanguage method directly.
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => handleDirectApiTest('en')}
                disabled={isTestingDirect}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                Test EN
              </button>
              <button
                onClick={() => handleDirectApiTest('fr')}
                disabled={isTestingDirect}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                Test FR
              </button>
              <button
                onClick={() => handleDirectApiTest('ar')}
                disabled={isTestingDirect}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                Test AR
              </button>
            </div>
          </div>

          {/* Test 3: React Query Mutation Hook */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Test 3: React Query Mutation Hook
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              This tests the useUpdatePreferredLanguage hook.
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => handleMutationTest('en')}
                disabled={updateLanguageMutation.isPending}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
              >
                Test EN Hook
              </button>
              <button
                onClick={() => handleMutationTest('fr')}
                disabled={updateLanguageMutation.isPending}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
              >
                Test FR Hook
              </button>
              <button
                onClick={() => handleMutationTest('ar')}
                disabled={updateLanguageMutation.isPending}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
              >
                Test AR Hook
              </button>
            </div>
            {updateLanguageMutation.isPending && (
              <p className="text-purple-600 dark:text-purple-400 mt-2">
                Mutation in progress...
              </p>
            )}
          </div>

          {/* API Endpoint Info */}
          <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              API Endpoint Information
            </h2>
            <div className="space-y-2 text-sm font-mono">
              <p><span className="font-bold">Endpoint:</span> POST /api/auth/user/update-prefered-language</p>
              <p><span className="font-bold">Authentication:</span> Required (Bearer token)</p>
              <p><span className="font-bold">Request Body:</span> {"{ \"preferedLanguage\": \"EN\" | \"AR\" | \"FR\" }"}</p>
              <p><span className="font-bold">Supported Languages:</span> EN, AR, FR</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestLanguageUpdate;
