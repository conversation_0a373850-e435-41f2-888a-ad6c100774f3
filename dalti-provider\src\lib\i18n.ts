import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enCommon from '../locales/en/common.json';
import enAuth from '../locales/en/auth.json';
import enDashboard from '../locales/en/dashboard.json';
import enForms from '../locales/en/forms.json';
import enErrors from '../locales/en/errors.json';
import enCalendar from '../locales/en/calendar.json';
import enManagement from '../locales/en/management.json';

import frCommon from '../locales/fr/common.json';
import frAuth from '../locales/fr/auth.json';
import frDashboard from '../locales/fr/dashboard.json';
import frForms from '../locales/fr/forms.json';
import frErrors from '../locales/fr/errors.json';
import frCalendar from '../locales/fr/calendar.json';
import frManagement from '../locales/fr/management.json';

import arCommon from '../locales/ar/common.json';
import arAuth from '../locales/ar/auth.json';
import arDashboard from '../locales/ar/dashboard.json';
import arForms from '../locales/ar/forms.json';
import arErrors from '../locales/ar/errors.json';
import arCalendar from '../locales/ar/calendar.json';
import arManagement from '../locales/ar/management.json';

// Language resources
const resources = {
  en: {
    common: enCommon,
    auth: enAuth,
    dashboard: enDashboard,
    forms: enForms,
    errors: enErrors,
    calendar: enCalendar,
    management: enManagement,
  },
  fr: {
    common: frCommon,
    auth: frAuth,
    dashboard: frDashboard,
    forms: frForms,
    errors: frErrors,
    calendar: frCalendar,
    management: frManagement,
  },
  ar: {
    common: arCommon,
    auth: arAuth,
    dashboard: arDashboard,
    forms: arForms,
    errors: arErrors,
    calendar: arCalendar,
    management: arManagement,
  },
};

// Supported languages configuration
export const supportedLanguages = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: 'EN', // UK flag for English language (distinct from USD 🇺🇸)
    dir: 'ltr',
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: 'FR', // France flag for French language
    dir: 'ltr',
  },
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: 'AR', // Saudi Arabia flag for Arabic language (distinct from DZD 🇩🇿)
    dir: 'rtl',
  },
] as const;

// Default language
export const defaultLanguage = 'en';

// RTL languages
export const rtlLanguages = ['ar'];

// Initialize i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: defaultLanguage,
    defaultNS: 'common',
    ns: ['common', 'auth', 'dashboard', 'forms', 'errors', 'calendar', 'management'],
    
    // Language detection options
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'dalti-language',
    },

    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
    },

    // Development options
    debug: import.meta.env.DEV,
    
    // React options
    react: {
      useSuspense: false, // Disable suspense for better error handling
    },

    // Pluralization
    pluralSeparator: '_',
    contextSeparator: '_',

    // Missing key handling
    saveMissing: import.meta.env.DEV,
    missingKeyHandler: (lng, ns, key) => {
      if (import.meta.env.DEV) {
        console.warn(`Missing translation key: ${ns}:${key} for language: ${lng}`);
      }
    },
  });

export default i18n;
