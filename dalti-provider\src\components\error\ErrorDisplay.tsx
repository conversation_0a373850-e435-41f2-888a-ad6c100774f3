import React from 'react';
import { ApiError } from '../../types';
import { useCommonTranslation } from '../../hooks/useTranslation';

interface ErrorDisplayProps {
  error: string | ApiError | Error | null;
  title?: string;
  showRetry?: boolean;
  onRetry?: () => void;
  className?: string;
  variant?: 'inline' | 'card' | 'banner';
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Reusable error display component
 */
export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  title,
  showRetry = false,
  onRetry,
  className = '',
  variant = 'card',
  size = 'md',
}) => {
  const { t } = useCommonTranslation();
  const defaultTitle = title || t('messages.error');

  if (!error) return null;

  const getErrorMessage = (): string => {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error instanceof Error) {
      return error.message;
    }
    
    if ('message' in error) {
      return error.message;
    }
    
    return 'An unexpected error occurred';
  };

  const getErrorDetails = (): string[] => {
    if (typeof error === 'object' && 'errors' in error && Array.isArray(error.errors)) {
      return error.errors;
    }
    return [];
  };

  const sizeClasses = {
    sm: 'text-sm p-3',
    md: 'text-base p-4',
    lg: 'text-lg p-6',
  };

  const variantClasses = {
    inline: 'text-red-600 dark:text-red-400',
    banner: 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg',
    card: 'bg-white dark:bg-gray-800 border border-red-200 dark:border-red-700 rounded-lg shadow-sm',
  };

  const iconSize = size === 'sm' ? 'w-4 h-4' : size === 'md' ? 'w-5 h-5' : 'w-6 h-6';

  if (variant === 'inline') {
    return (
      <div className={`flex items-center space-x-2 ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}>
        <svg className={`${iconSize} flex-shrink-0`} fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
            clipRule="evenodd"
          />
        </svg>
        <span>{getErrorMessage()}</span>
      </div>
    );
  }

  return (
    <div className={`${sizeClasses[size]} ${variantClasses[variant]} ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <svg
            className={`${iconSize} text-red-500 dark:text-red-400`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
            {defaultTitle}
          </h3>
          
          <div className="mt-1 text-sm text-red-700 dark:text-red-300">
            <p>{getErrorMessage()}</p>
            
            {getErrorDetails().length > 0 && (
              <ul className="mt-2 list-disc list-inside space-y-1">
                {getErrorDetails().map((detail, index) => (
                  <li key={index}>{detail}</li>
                ))}
              </ul>
            )}
          </div>
          
          {showRetry && onRetry && (
            <div className="mt-3">
              <button
                onClick={onRetry}
                className="text-sm bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-800 dark:text-red-200 font-medium py-1 px-3 rounded transition-colors"
              >
                {t('messages.tryAgain')}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Simple error message component for form fields
 */
export const FieldError: React.FC<{ error?: string }> = ({ error }) => {
  if (!error) return null;
  
  return (
    <p className="mt-1 text-sm text-red-600 dark:text-red-400 flex items-center space-x-1">
      <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
          clipRule="evenodd"
        />
      </svg>
      <span>{error}</span>
    </p>
  );
};

/**
 * Network error component
 */
export const NetworkError: React.FC<{ onRetry?: () => void }> = ({ onRetry }) => (
  <ErrorDisplay
    error="Unable to connect to the server. Please check your internet connection."
    title="Connection Error"
    showRetry={!!onRetry}
    onRetry={onRetry}
    variant="card"
  />
);

/**
 * Not found error component
 */
export const NotFoundError: React.FC<{ resource?: string }> = ({ resource = 'resource' }) => (
  <ErrorDisplay
    error={`The ${resource} you're looking for could not be found.`}
    title="Not Found"
    variant="card"
  />
);

/**
 * Permission error component
 */
export const PermissionError: React.FC = () => (
  <ErrorDisplay
    error="You don't have permission to access this resource."
    title="Access Denied"
    variant="card"
  />
);

export default ErrorDisplay;
