import { Appointment } from '../types';

export interface TimeSlotGroup {
  startTime: string;
  endTime: string;
  appointments: Appointment[];
}

/**
 * Groups appointments that overlap in time into the same time slot
 * Uses a more aggressive grouping strategy for calendar display
 */
export function groupAppointmentsByTimeSlot(appointments: Appointment[]): TimeSlotGroup[] {
  if (!appointments || appointments.length === 0) {
    return [];
  }

  // Sort appointments by start time
  const sortedAppointments = [...appointments].sort((a, b) =>
    new Date(a.expectedAppointmentStartTime).getTime() -
    new Date(b.expectedAppointmentStartTime).getTime()
  );

  const groups: TimeSlotGroup[] = [];

  for (const appointment of sortedAppointments) {
    const appointmentStart = new Date(appointment.expectedAppointmentStartTime);
    const appointmentEnd = new Date(appointment.expectedAppointmentEndTime);

    // Find if this appointment overlaps with any existing group
    // Use a more lenient overlap detection for better grouping
    let foundGroup = false;

    for (const group of groups) {
      const groupStart = new Date(group.startTime);
      const groupEnd = new Date(group.endTime);

      // More aggressive grouping: check if appointments are within the same time slot
      // Consider appointments that start within 15 minutes of each other as overlapping
      const timeDifference = Math.abs(appointmentStart.getTime() - groupStart.getTime());
      const isWithinTimeSlot = timeDifference <= 15 * 60 * 1000; // 15 minutes

      // Check for overlap or close proximity
      const hasOverlap = appointmentStart < groupEnd && appointmentEnd > groupStart;

      if (hasOverlap || isWithinTimeSlot) {
        // Add to existing group and extend the time range
        group.appointments.push(appointment);
        group.startTime = new Date(Math.min(groupStart.getTime(), appointmentStart.getTime())).toISOString();
        group.endTime = new Date(Math.max(groupEnd.getTime(), appointmentEnd.getTime())).toISOString();
        foundGroup = true;
        break;
      }
    }

    // If no overlapping group found, create a new one
    if (!foundGroup) {
      groups.push({
        startTime: appointment.expectedAppointmentStartTime,
        endTime: appointment.expectedAppointmentEndTime,
        appointments: [appointment]
      });
    }
  }

  return groups;
}

/**
 * Groups appointments by calendar time slots (e.g., 9:00-9:30, 9:30-10:00)
 * This ensures appointments in the same time slot are grouped together
 */
export function groupAppointmentsByTimeSlots(appointments: Appointment[], slotDurationMinutes: number = 30): TimeSlotGroup[] {
  if (!appointments || appointments.length === 0) {
    return [];
  }

  // Create a map to group appointments by their time slot
  const slotMap = new Map<string, Appointment[]>();

  for (const appointment of appointments) {
    const startTime = new Date((appointment as any).expectedAppointmentStartTime);

    // Calculate which time slot this appointment belongs to
    const slotStart = getTimeSlotStart(startTime, slotDurationMinutes);
    const slotEnd = new Date(slotStart.getTime() + slotDurationMinutes * 60 * 1000);

    // Create a unique key for this time slot
    const slotKey = slotStart.toISOString();

    if (!slotMap.has(slotKey)) {
      slotMap.set(slotKey, []);
    }
    slotMap.get(slotKey)!.push(appointment);
  }

  // Convert map to TimeSlotGroup array
  const groups: TimeSlotGroup[] = [];

  for (const [slotKey, slotAppointments] of slotMap.entries()) {
    const slotStart = new Date(slotKey);
    const slotEnd = new Date(slotStart.getTime() + slotDurationMinutes * 60 * 1000);

    groups.push({
      startTime: slotStart.toISOString(),
      endTime: slotEnd.toISOString(),
      appointments: slotAppointments
    });
  }

  // Sort groups by start time
  groups.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());

  return groups;
}

/**
 * Calculate the start time of the time slot that contains the given time
 */
function getTimeSlotStart(time: Date, slotDurationMinutes: number): Date {
  const slotStart = new Date(time);

  // Get the number of minutes since midnight
  const minutesSinceMidnight = slotStart.getHours() * 60 + slotStart.getMinutes();

  // Calculate which slot this time falls into
  const slotIndex = Math.floor(minutesSinceMidnight / slotDurationMinutes);

  // Calculate the start time of this slot
  const slotStartMinutes = slotIndex * slotDurationMinutes;
  const slotStartHours = Math.floor(slotStartMinutes / 60);
  const slotStartMins = slotStartMinutes % 60;

  // Set the time to the slot start
  slotStart.setHours(slotStartHours, slotStartMins, 0, 0);

  return slotStart;
}



/**
 * Checks if two time ranges overlap
 */
export function timeRangesOverlap(
  start1: string | Date, 
  end1: string | Date, 
  start2: string | Date, 
  end2: string | Date
): boolean {
  const s1 = new Date(start1);
  const e1 = new Date(end1);
  const s2 = new Date(start2);
  const e2 = new Date(end2);

  return s1 < e2 && e1 > s2;
}

/**
 * Formats time slot duration for display
 */
export function formatTimeSlotDuration(startTime: string, endTime: string): string {
  const start = new Date(startTime);
  const end = new Date(endTime);
  const durationMs = end.getTime() - start.getTime();
  const durationMinutes = Math.round(durationMs / (1000 * 60));

  if (durationMinutes < 60) {
    return `${durationMinutes}min`;
  } else {
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    return minutes > 0 ? `${hours}h ${minutes}min` : `${hours}h`;
  }
}

/**
 * Gets the time slot interval in minutes from a duration string
 */
export function getSlotIntervalMinutes(slotDuration: string): number {
  // Parse duration like "00:30:00" or "30"
  if (slotDuration.includes(':')) {
    const parts = slotDuration.split(':');
    const hours = parseInt(parts[0]) || 0;
    const minutes = parseInt(parts[1]) || 0;
    return hours * 60 + minutes;
  }
  return parseInt(slotDuration) || 30;
}

/**
 * Rounds a time to the nearest slot interval
 */
export function roundToSlotInterval(time: Date, intervalMinutes: number): Date {
  const minutes = time.getMinutes();
  const roundedMinutes = Math.round(minutes / intervalMinutes) * intervalMinutes;
  
  const rounded = new Date(time);
  rounded.setMinutes(roundedMinutes);
  rounded.setSeconds(0);
  rounded.setMilliseconds(0);
  
  return rounded;
}

/**
 * Generates time slots for a given day and interval
 */
export function generateTimeSlots(
  startTime: string, 
  endTime: string, 
  intervalMinutes: number
): { start: string; end: string }[] {
  const slots: { start: string; end: string }[] = [];
  
  // Parse start and end times (format: "HH:MM")
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);
  
  const today = new Date();
  today.setHours(startHour, startMinute, 0, 0);
  
  const endDate = new Date();
  endDate.setHours(endHour, endMinute, 0, 0);
  
  let current = new Date(today);
  
  while (current < endDate) {
    const slotStart = new Date(current);
    const slotEnd = new Date(current.getTime() + intervalMinutes * 60000);
    
    if (slotEnd <= endDate) {
      slots.push({
        start: slotStart.toTimeString().slice(0, 5), // "HH:MM"
        end: slotEnd.toTimeString().slice(0, 5)
      });
    }
    
    current = slotEnd;
  }
  
  return slots;
}

/**
 * Calculates the height percentage for an appointment based on its duration and slot interval
 */
export function calculateEventHeight(
  appointmentStart: string,
  appointmentEnd: string,
  slotIntervalMinutes: number
): number {
  const start = new Date(appointmentStart);
  const end = new Date(appointmentEnd);
  const durationMs = end.getTime() - start.getTime();
  const durationMinutes = durationMs / (1000 * 60);
  
  // Calculate height as percentage of slot interval
  const heightPercentage = (durationMinutes / slotIntervalMinutes) * 100;
  
  // Ensure minimum height for visibility and maximum of 100%
  return Math.max(20, Math.min(100, heightPercentage));
}

/**
 * Gets the position offset for an appointment within its time slot
 */
export function calculateEventPosition(
  appointmentStart: string,
  slotStart: string,
  slotIntervalMinutes: number
): number {
  const appointmentTime = new Date(appointmentStart);
  const slotTime = new Date(slotStart);
  
  const offsetMs = appointmentTime.getTime() - slotTime.getTime();
  const offsetMinutes = offsetMs / (1000 * 60);
  
  // Calculate position as percentage of slot interval
  const positionPercentage = (offsetMinutes / slotIntervalMinutes) * 100;
  
  return Math.max(0, Math.min(80, positionPercentage)); // Max 80% to leave space for content
}
