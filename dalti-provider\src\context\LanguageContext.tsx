import React, { createContext, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';
import { supportedLanguages, rtlLanguages, defaultLanguage } from '../lib/i18n';
import { UserService } from '../services/user.service';
import { ErrorLogger } from '../lib/error-utils';

// Types
export type LanguageCode = 'en' | 'fr' | 'ar';
export type TextDirection = 'ltr' | 'rtl';

export interface LanguageInfo {
  code: LanguageCode;
  name: string;
  nativeName: string;
  flag: string;
  dir: TextDirection;
}

interface LanguageContextType {
  currentLanguage: LanguageCode;
  languageInfo: LanguageInfo;
  direction: TextDirection;
  isRTL: boolean;
  supportedLanguages: readonly LanguageInfo[];
  changeLanguage: (languageCode: LanguageCode) => Promise<void>;
  isChangingLanguage: boolean;
}

// Create context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Language Provider Props
interface LanguageProviderProps {
  children: React.ReactNode;
}

// Language Provider Component
export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const { i18n } = useTranslation();
  const queryClient = useQueryClient();
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<LanguageCode>(
    (i18n.language as LanguageCode) || defaultLanguage
  );

  // Get current language info
  const languageInfo = supportedLanguages.find(lang => lang.code === currentLanguage) || supportedLanguages[0];
  const direction = languageInfo.dir;
  const isRTL = rtlLanguages.includes(currentLanguage);

  // Change language function
  const changeLanguage = async (languageCode: LanguageCode) => {
    if (languageCode === currentLanguage) return;

    setIsChangingLanguage(true);

    try {
      // Update i18n and local state first for immediate UI feedback
      await i18n.changeLanguage(languageCode);
      setCurrentLanguage(languageCode);

      // Update document direction and language
      document.documentElement.lang = languageCode;
      document.documentElement.dir = rtlLanguages.includes(languageCode) ? 'rtl' : 'ltr';

      // Store language preference locally
      localStorage.setItem('dalti-language', languageCode);

      // Update preferred language on backend (fire and forget - don't block UI)
      UserService.updatePreferredLanguage(languageCode).catch((error) => {
        // Log error but don't show to user to avoid interrupting language switch UX
        ErrorLogger.log(error, {
          context: 'changeLanguage',
          languageCode,
          message: 'Failed to sync language preference with backend'
        });
        console.warn('⚠️ Failed to sync language preference with backend:', error.message);
      });

      // Invalidate all queries to refetch data with new language
      console.log('🔄 Invalidating all queries due to language change:', languageCode);
      queryClient.invalidateQueries();

      // Dispatch custom event for other components to listen to
      window.dispatchEvent(new CustomEvent('languageChanged', {
        detail: { language: languageCode, direction: rtlLanguages.includes(languageCode) ? 'rtl' : 'ltr' }
      }));

    } catch (error) {
      console.error('Failed to change language:', error);
      ErrorLogger.log(error, { context: 'changeLanguage', languageCode });
    } finally {
      setIsChangingLanguage(false);
    }
  };

  // Initialize language on mount
  useEffect(() => {
    const initializeLanguage = () => {
      const savedLanguage = localStorage.getItem('dalti-language') as LanguageCode;
      const browserLanguage = navigator.language.split('-')[0] as LanguageCode;
      
      // Determine initial language
      let initialLanguage = defaultLanguage;
      
      if (savedLanguage && supportedLanguages.some(lang => lang.code === savedLanguage)) {
        initialLanguage = savedLanguage;
      } else if (supportedLanguages.some(lang => lang.code === browserLanguage)) {
        initialLanguage = browserLanguage;
      }
      
      // Set initial language if different from current
      if (initialLanguage !== currentLanguage) {
        changeLanguage(initialLanguage);
      } else {
        // Ensure document attributes are set correctly
        document.documentElement.lang = currentLanguage;
        document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
      }
    };

    initializeLanguage();
  }, []);

  // Listen to i18n language changes
  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      const newLanguage = lng as LanguageCode;
      if (newLanguage !== currentLanguage && supportedLanguages.some(lang => lang.code === newLanguage)) {
        setCurrentLanguage(newLanguage);
        document.documentElement.lang = newLanguage;
        document.documentElement.dir = rtlLanguages.includes(newLanguage) ? 'rtl' : 'ltr';
      }
    };

    i18n.on('languageChanged', handleLanguageChange);
    
    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n, currentLanguage]);

  // Context value
  const contextValue: LanguageContextType = {
    currentLanguage,
    languageInfo,
    direction,
    isRTL,
    supportedLanguages,
    changeLanguage,
    isChangingLanguage,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  
  return context;
};

// Utility hook for RTL-aware styling
export const useRTL = () => {
  const { isRTL, direction } = useLanguage();
  
  return {
    isRTL,
    direction,
    // Helper functions for RTL-aware styling
    marginStart: (value: string) => isRTL ? { marginRight: value } : { marginLeft: value },
    marginEnd: (value: string) => isRTL ? { marginLeft: value } : { marginRight: value },
    paddingStart: (value: string) => isRTL ? { paddingRight: value } : { paddingLeft: value },
    paddingEnd: (value: string) => isRTL ? { paddingLeft: value } : { paddingRight: value },
    textAlign: (align: 'start' | 'end') => {
      if (align === 'start') return isRTL ? 'right' : 'left';
      if (align === 'end') return isRTL ? 'left' : 'right';
      return align;
    },
  };
};

export default LanguageContext;
