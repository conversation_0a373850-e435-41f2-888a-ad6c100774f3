import React, { useEffect, useRef } from 'react';
import { SearchResult } from '../../hooks/useGlobalSearch';
import { useCommonTranslation } from '../../hooks/useTranslation';
import { useRTL, useLanguage } from '../../context/LanguageContext';

interface GlobalSearchDropdownProps {
  results: SearchResult[];
  isOpen: boolean;
  onSelect: (result: SearchResult) => void;
  onClose: () => void;
  query: string;
  showRecentSearches?: boolean;
  onClearRecent?: () => void;
}

const categoryColors = {
  page: 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300',
  feature: 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300',
  action: 'bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300'
};

const GlobalSearchDropdown: React.FC<GlobalSearchDropdownProps> = ({
  results,
  isOpen,
  onSelect,
  onClose,
  query,
  showRecentSearches = false,
  onClearRecent
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Translation and RTL hooks
  const { t } = useCommonTranslation();
  const { isRTL, direction } = useRTL();
  const { currentLanguage } = useLanguage();

  // Category labels with translations
  const translatedCategoryLabels = {
    page: t('search.categories.pages'),
    feature: t('search.categories.features'),
    action: t('search.categories.actions')
  };

  // Language labels for indicators
  const languageLabels = {
    en: 'EN',
    ar: 'ع',
    fr: 'FR'
  };
  const [selectedIndex, setSelectedIndex] = React.useState(0);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setSelectedIndex(prev => 
            prev < results.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : results.length - 1
          );
          break;
        case 'Enter':
          event.preventDefault();
          if (results[selectedIndex]) {
            onSelect(results[selectedIndex]);
          }
          break;
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex, onSelect, onClose]);

  // Reset selected index when results change
  useEffect(() => {
    setSelectedIndex(0);
  }, [results]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  // Group results by category
  const groupedResults = results.reduce((acc, result, index) => {
    const category = result.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push({ ...result, originalIndex: index });
    return acc;
  }, {} as Record<string, (SearchResult & { originalIndex: number })[]>);

  const highlightText = (text: string, query: string) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 text-current">
          {part}
        </mark>
      ) : part
    );
  };

  return (
    <div
      ref={dropdownRef}
      className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-96 overflow-hidden z-50"
      dir={direction}
    >
      {results.length === 0 ? (
        <div className="p-4 text-center text-gray-500 dark:text-gray-400">
          <div className="text-2xl mb-2">🔍</div>
          {query ? (
            <>
              <p className="text-sm">{t('search.noResults', { query })}</p>
              <p className="text-xs mt-1">{t('search.noResultsHint')}</p>
            </>
          ) : (
            <>
              <p className="text-sm">{t('search.startTyping')}</p>
              <p className="text-xs mt-1">{t('search.startTypingHint')}</p>
            </>
          )}
        </div>
      ) : (
        <div className="max-h-96 overflow-y-auto">
          {/* Recent searches header */}
          {showRecentSearches && !query && results.length > 0 && (
            <div className={`px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 border-b border-gray-100 dark:border-gray-700 flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <span>{t('search.recentSearches')}</span>
              {onClearRecent && (
                <button
                  onClick={onClearRecent}
                  className="text-xs text-brand-600 dark:text-brand-400 hover:text-brand-700 dark:hover:text-brand-300"
                >
                  {t('search.clear')}
                </button>
              )}
            </div>
          )}

          {Object.entries(groupedResults).map(([category, categoryResults]) => (
            <div key={category}>
              {/* Only show category headers when not showing recent searches */}
              {(!showRecentSearches || query) && (
                <div className="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 border-b border-gray-100 dark:border-gray-700">
                  {translatedCategoryLabels[category as keyof typeof translatedCategoryLabels]}
                </div>
              )}
              {categoryResults.map((result) => (
                <button
                  key={result.id}
                  onClick={() => onSelect(result)}
                  className={`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0 transition-colors ${
                    selectedIndex === result.originalIndex
                      ? 'bg-brand-50 dark:bg-brand-900/20 border-brand-200 dark:border-brand-800'
                      : ''
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <div className="text-lg mt-0.5 flex-shrink-0">
                      {result.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {highlightText(result.title, query)}
                        </h4>
                        <div className="flex items-center gap-1">
                          {/* Language indicator if matched in different language */}
                          {result.matchedLanguage && result.matchedLanguage !== currentLanguage && (
                            <span className="px-1.5 py-0.5 text-xs font-medium rounded bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
                              {languageLabels[result.matchedLanguage]}
                            </span>
                          )}
                          <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                            categoryColors[result.category]
                          }`}>
                            {translatedCategoryLabels[result.category]}
                          </span>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 overflow-hidden" style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical'
                      }}>
                        {highlightText(result.description, query)}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ))}
          
          {/* Footer with keyboard shortcuts */}
          <div className="px-4 py-2 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-xs">↑↓</kbd>
                  Navigate
                </span>
                <span className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-xs">↵</kbd>
                  Select
                </span>
                <span className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-xs">Esc</kbd>
                  Close
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlobalSearchDropdown;
