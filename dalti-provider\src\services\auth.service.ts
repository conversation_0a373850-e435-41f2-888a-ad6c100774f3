import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  LoginRequest,
  LoginResponse,
  OtpRequest,
  VerifyOtpRegisterRequest,
  PasswordResetRequest,
  PasswordResetVerifyRequest,
  PasswordResetVerifyResponse,
  PasswordResetConfirmRequest,
  User,
  Provider,
  ProviderCategory,
  UsedCreditsResponse,
} from '../types';

/**
 * Authentication service for provider-related auth operations
 */
export class AuthService {
  /**
   * Request OTP for email verification
   */
  static async requestEmailOtp(data: OtpRequest): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(
      config.endpoints.auth.requestEmailOtp,
      data
    );
    return response.data;
  }

  /**
   * Request OTP for phone verification
   */
  static async requestPhoneOtp(data: OtpRequest): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(
      config.endpoints.auth.requestPhoneOtp,
      data
    );
    return response.data;
  }

  /**
   * Verify OTP and complete provider registration
   */
  static async verifyOtpAndRegister(data: VerifyOtpRegisterRequest): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>(
      config.endpoints.auth.verifyOtpRegister,
      data
    );
    return response.data;
  }

  /**
   * Provider login
   */
  static async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await apiClient.post<LoginResponse>(
      config.endpoints.auth.login,
      data
    );
    return response.data;
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(refreshToken: string): Promise<{ token: string; refreshToken: string }> {
    const response = await apiClient.post<{ token: string; refreshToken: string }>(
      config.endpoints.auth.refreshToken,
      { refreshToken }
    );
    return response.data;
  }

  /**
   * Request password reset OTP
   */
  static async requestPasswordReset(data: PasswordResetRequest): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(
      config.endpoints.auth.passwordResetRequest,
      data
    );
    return response.data;
  }

  /**
   * Verify password reset OTP
   */
  static async verifyPasswordResetOtp(data: PasswordResetVerifyRequest): Promise<PasswordResetVerifyResponse> {
    const response = await apiClient.post<PasswordResetVerifyResponse>(
      config.endpoints.auth.passwordResetVerify,
      data
    );
    return response.data;
  }

  /**
   * Reset password with OTP
   */
  static async resetPassword(data: PasswordResetConfirmRequest): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(
      config.endpoints.auth.passwordReset,
      data
    );
    return response.data;
  }

  /**
   * Get provider categories for registration
   */
  static async getProviderCategories(lang?: string): Promise<ProviderCategory[]> {
    const params = lang ? { lang } : {};
    const response = await apiClient.get<ProviderCategory[]>('/api/provider-categories', { params });
    return response.data;
  }

  /**
   * Logout (client-side token cleanup)
   */
  static logout(): void {
    localStorage.removeItem(config.auth.tokenKey);
    localStorage.removeItem(config.auth.refreshTokenKey);
    localStorage.removeItem(config.auth.userKey);
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const token = localStorage.getItem(config.auth.tokenKey);
    if (!token) return false;

    try {
      // Check if we have user data (indicates a complete session)
      const userData = this.getStoredUser();
      if (!userData) return false;

      // Check if token is a JWT (contains dots) or a sessionId
      if (token.includes('.')) {
        // It's a JWT token - parse and validate expiration
        try {
          const tokenData = JSON.parse(atob(token.split('.')[1]));
          const currentTime = Date.now() / 1000;

          // Check if token is expired
          if (tokenData.exp && tokenData.exp <= currentTime) {
            // Token is expired, clear storage
            this.logout();
            return false;
          }
        } catch (jwtError) {
          // JWT parsing failed, but don't logout - might be malformed JWT
          console.warn('JWT parsing failed:', jwtError);
        }
      }
      // If it's a sessionId (no dots), we just check if it exists and has user data
      // The session validity will be checked by isSessionValid()

      return true;
    } catch (error) {
      // If there's any other error, clear storage and return false
      console.error('Authentication check failed:', error);
      this.logout();
      return false;
    }
  }

  /**
   * Get stored user data
   */
  static getStoredUser(): { user: User; provider: Provider } | null {
    try {
      const userData = localStorage.getItem(config.auth.userKey);
      return userData ? JSON.parse(userData) : null;
    } catch {
      return null;
    }
  }

  /**
   * Store user data
   */
  static storeUserData(user: User, provider: Provider): void {
    localStorage.setItem(config.auth.userKey, JSON.stringify({ user, provider }));
  }

  /**
   * Store authentication tokens
   */
  static storeTokens(token: string, refreshToken?: string): void {
    localStorage.setItem(config.auth.tokenKey, token);
    if (refreshToken) {
      localStorage.setItem(config.auth.refreshTokenKey, refreshToken);
    }
  }

  /**
   * Get stored token
   */
  static getToken(): string | null {
    return localStorage.getItem(config.auth.tokenKey);
  }

  /**
   * Update last activity timestamp
   */
  static updateLastActivity(): void {
    localStorage.setItem('provider_last_activity', Date.now().toString());
  }

  /**
   * Check if session is still valid based on activity
   */
  static isSessionValid(): boolean {
    const lastActivity = localStorage.getItem('provider_last_activity');
    if (!lastActivity) return false;

    const timeSinceLastActivity = Date.now() - parseInt(lastActivity);
    return timeSinceLastActivity < config.auth.sessionTimeout;
  }

  /**
   * Get used credits data for the current month
   */
  static async getUsedCredits(): Promise<UsedCreditsResponse> {
    console.log('🌐 AuthService.getUsedCredits: Making API call to:', config.endpoints.payment.usage);
    const response = await apiClient.get<UsedCreditsResponse>(
      config.endpoints.payment.usage
    );
    console.log('🌐 AuthService.getUsedCredits: Raw API response:', response);
    return response.data;
  }
}
