# API Configuration
VITE_API_BASE_URL_DEV=https://dapi-test.adscloud.org:8443
VITE_API_BASE_URL_PROD=https://dapi.adscloud.org

# Environment
VITE_NODE_ENV=development

# App Configuration
VITE_APP_NAME=Provider Dashboard
VITE_APP_VERSION=1.0.0

# Debug Settings
VITE_DEBUG_API=true
VITE_DEBUG_AUTH=true

# Chargily Pay Feature Flags
VITE_ENABLE_CHARGILY=false
VITE_ENABLE_CHARGILY_AUTO_SELECTION=false
VITE_ENABLE_CHARGILY_PROD=false
VITE_ENABLE_CHARGILY_AUTO_SELECTION_PROD=false

# Location Detection
VITE_AUTO_DETECT_LOCATION=false
VITE_AUTO_DETECT_LOCATION_PROD=false
VITE_MOCK_LOCATION=false
VITE_MOCK_ALGERIAN_USER=true

# Payment Debug & Testing
VITE_DEBUG_PAYMENT=false
VITE_PAYMENT_TEST_MODE=false

# Rollout Control
VITE_GRADUAL_ROLLOUT=false
VITE_ROLLOUT_PERCENTAGE=0
VITE_ALGERIAN_ONLY=false

# User Lists (comma-separated emails)
VITE_BETA_USERS=
VITE_ADMIN_USERS=

# Chargily Pay Configuration
VITE_CHARGILY_PUBLIC_KEY=
VITE_CHARGILY_WEBHOOK_SECRET=

# LemonSqueezy Configuration
VITE_LEMONSQUEEZY_STORE_ID=
VITE_LEMONSQUEEZY_API_KEY=
