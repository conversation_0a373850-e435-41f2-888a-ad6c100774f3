import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook to handle aggressive dashboard refresh when page becomes visible
 * This ensures that when users navigate back to the dashboard, all data is fresh
 */
export const useDashboardRefresh = () => {
  const queryClient = useQueryClient();
  const lastRefreshRef = useRef<number>(0);
  const isInitialMount = useRef(true);

  useEffect(() => {
    const handleVisibilityChange = () => {
      // Only refresh if the page becomes visible and it's been more than 10 seconds since last refresh
      if (!document.hidden && Date.now() - lastRefreshRef.current > 10000) {
        console.log('🔄 Dashboard page became visible - refreshing all data');
        
        // Force refresh all dashboard-related queries
        queryClient.invalidateQueries({ queryKey: ['dashboard'] });
        queryClient.invalidateQueries({ queryKey: ['appointments'] });
        
        // Force immediate refetch
        queryClient.refetchQueries({ queryKey: ['dashboard'] });
        queryClient.refetchQueries({ queryKey: ['appointments'] });
        
        lastRefreshRef.current = Date.now();
      }
    };

    const handleFocus = () => {
      // Refresh when window regains focus (user switches back to tab)
      if (Date.now() - lastRefreshRef.current > 5000) {
        console.log('🔄 Dashboard window focused - refreshing data');
        
        queryClient.invalidateQueries({ queryKey: ['dashboard'] });
        queryClient.invalidateQueries({ queryKey: ['appointments'] });
        
        lastRefreshRef.current = Date.now();
      }
    };

    // Initial refresh on mount (when navigating to dashboard)
    if (isInitialMount.current) {
      console.log('🔄 Dashboard mounted - initial data refresh');
      
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      queryClient.invalidateQueries({ queryKey: ['appointments'] });
      queryClient.refetchQueries({ queryKey: ['dashboard'] });
      queryClient.refetchQueries({ queryKey: ['appointments'] });
      
      lastRefreshRef.current = Date.now();
      isInitialMount.current = false;
    }

    // Listen for visibility changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [queryClient]);

  // Manual refresh function
  const forceRefresh = () => {
    console.log('🔄 Manual dashboard refresh triggered');
    
    queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    queryClient.invalidateQueries({ queryKey: ['appointments'] });
    queryClient.refetchQueries({ queryKey: ['dashboard'] });
    queryClient.refetchQueries({ queryKey: ['appointments'] });
    
    lastRefreshRef.current = Date.now();
  };

  return { forceRefresh };
};
