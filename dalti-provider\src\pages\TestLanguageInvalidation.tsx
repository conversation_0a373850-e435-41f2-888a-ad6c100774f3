import React, { useState } from 'react';
import { useLanguage } from '../context/LanguageContext';
import { useServices } from '../hooks/useServices';
import { useLocations } from '../hooks/useLocations';
import { useAppointments } from '../hooks/useAppointments';
import { useNotifications } from '../hooks/useNotifications';
import { useLanguageQueryInvalidation } from '../hooks/useLanguageQueryInvalidation';
import { LanguageCode } from '../context/LanguageContext';
import PageMeta from '../components/common/PageMeta';

const TestLanguageInvalidation: React.FC = () => {
  const { currentLanguage, changeLanguage, isChangingLanguage } = useLanguage();
  const [testResults, setTestResults] = useState<string[]>([]);
  
  // Query hooks to test invalidation
  const { data: services, isLoading: servicesLoading, dataUpdatedAt: servicesUpdatedAt } = useServices();
  const { data: locations, isLoading: locationsLoading, dataUpdatedAt: locationsUpdatedAt } = useLocations();
  const { data: appointments, isLoading: appointmentsLoading, dataUpdatedAt: appointmentsUpdatedAt } = useAppointments();
  const { data: notifications, isLoading: notificationsLoading, dataUpdatedAt: notificationsUpdatedAt } = useNotifications();
  
  // Manual invalidation hooks
  const {
    invalidateAllQueries,
    invalidateTranslatableQueries,
    clearAllQueries,
    refetchAllQueries,
  } = useLanguageQueryInvalidation();

  const addTestResult = (result: string) => {
    setTestResults(prev => [`${new Date().toLocaleTimeString()}: ${result}`, ...prev.slice(0, 9)]);
  };

  const handleLanguageChange = async (languageCode: LanguageCode) => {
    const beforeTimestamps = {
      services: servicesUpdatedAt,
      locations: locationsUpdatedAt,
      appointments: appointmentsUpdatedAt,
      notifications: notificationsUpdatedAt,
    };

    addTestResult(`🌐 Changing language from ${currentLanguage} to ${languageCode}`);
    
    try {
      await changeLanguage(languageCode);
      addTestResult(`✅ Language changed successfully to ${languageCode}`);
      
      // Check if timestamps changed (indicating data was refetched)
      setTimeout(() => {
        const afterTimestamps = {
          services: servicesUpdatedAt,
          locations: locationsUpdatedAt,
          appointments: appointmentsUpdatedAt,
          notifications: notificationsUpdatedAt,
        };

        Object.entries(beforeTimestamps).forEach(([key, beforeTime]) => {
          const afterTime = afterTimestamps[key as keyof typeof afterTimestamps];
          if (afterTime && afterTime > beforeTime) {
            addTestResult(`🔄 ${key} data was refetched (${beforeTime} -> ${afterTime})`);
          } else {
            addTestResult(`⚠️ ${key} data was NOT refetched`);
          }
        });
      }, 1000);
    } catch (error) {
      addTestResult(`❌ Failed to change language: ${error}`);
    }
  };

  const testManualInvalidation = (type: string) => {
    addTestResult(`🧪 Testing manual ${type} invalidation`);
    
    switch (type) {
      case 'all':
        invalidateAllQueries();
        break;
      case 'translatable':
        invalidateTranslatableQueries();
        break;
      case 'clear':
        clearAllQueries();
        break;
      case 'refetch':
        refetchAllQueries();
        break;
    }
    
    addTestResult(`✅ Manual ${type} invalidation triggered`);
  };

  const formatTimestamp = (timestamp?: number) => {
    return timestamp ? new Date(timestamp).toLocaleTimeString() : 'Never';
  };

  return (
    <div className="p-6 space-y-6">
      <PageMeta
        title="Language Invalidation Test - Dalti Provider Dashboard"
        description="Testing query invalidation when language changes"
      />

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Language Query Invalidation Test
        </h1>

        {/* Current Language Info */}
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Current Language: {currentLanguage.toUpperCase()}
          </h2>
          <p className="text-blue-700 dark:text-blue-300">
            {isChangingLanguage ? 'Changing language...' : 'Ready to test language changes'}
          </p>
        </div>

        {/* Language Change Buttons */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Test Language Changes
          </h3>
          <div className="flex space-x-3">
            {(['en', 'fr', 'ar'] as LanguageCode[]).map((lang) => (
              <button
                key={lang}
                onClick={() => handleLanguageChange(lang)}
                disabled={isChangingLanguage || currentLanguage === lang}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  currentLanguage === lang
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50'
                }`}
              >
                {lang.toUpperCase()}
              </button>
            ))}
          </div>
        </div>

        {/* Manual Invalidation Tests */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Manual Invalidation Tests
          </h3>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => testManualInvalidation('all')}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Invalidate All
            </button>
            <button
              onClick={() => testManualInvalidation('translatable')}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700"
            >
              Invalidate Translatable
            </button>
            <button
              onClick={() => testManualInvalidation('clear')}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Clear Cache
            </button>
            <button
              onClick={() => testManualInvalidation('refetch')}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              Refetch All
            </button>
          </div>
        </div>

        {/* Query Status */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Query Status
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { name: 'Services', loading: servicesLoading, count: services?.length || 0, updated: servicesUpdatedAt },
              { name: 'Locations', loading: locationsLoading, count: locations?.length || 0, updated: locationsUpdatedAt },
              { name: 'Appointments', loading: appointmentsLoading, count: appointments?.appointments?.length || 0, updated: appointmentsUpdatedAt },
              { name: 'Notifications', loading: notificationsLoading, count: notifications?.notifications?.length || 0, updated: notificationsUpdatedAt },
            ].map((query) => (
              <div key={query.name} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">{query.name}</h4>
                  {query.loading && (
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Count: {query.count}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  Updated: {formatTimestamp(query.updated)}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Test Results */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Test Results
          </h3>
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 italic">
                No test results yet. Try changing the language or running manual tests.
              </p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono text-gray-700 dark:text-gray-300">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestLanguageInvalidation;
