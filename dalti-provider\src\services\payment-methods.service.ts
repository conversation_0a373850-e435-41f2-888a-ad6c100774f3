import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  PaymentMethodsResponse,
  ChargilyCheckoutResponse,
  ChargilyPaymentStatusResponse,
  CheckoutSessionRequest,
  PaymentMethod,
  PaymentProcessor,
  PaymentMethodType,
} from '../types';
import { parsePaymentError, logPaymentError } from '../utils/payment-errors.utils';
import { HardcodedPaymentMethodsService } from './hardcoded-payment-methods.service';

/**
 * Payment Methods service for Chargily Pay integration
 * Handles payment method detection, Chargily checkout, and multi-processor routing
 */
export class PaymentMethodsService {
  /**
   * Get available payment methods based on user location and preferences
   * Uses hardcoded payment methods since backend doesn't provide methods endpoint
   */
  static async getAvailablePaymentMethods(userCountryCode?: string): Promise<PaymentMethodsResponse> {
    try {
      return await HardcodedPaymentMethodsService.getAvailablePaymentMethods(userCountryCode);
    } catch (error) {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'getAvailablePaymentMethods');
      throw paymentError;
    }
  }

  /**
   * Create a Chargily Pay checkout session
   * Requires authentication
   */
  static async createChargilyCheckout(data: CheckoutSessionRequest): Promise<ChargilyCheckoutResponse> {
    try {
      const response = await apiClient.post<ChargilyCheckoutResponse>(
        config.endpoints.payment.chargilyCheckout,
        data
      );
      return response.data;
    } catch (error) {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'createChargilyCheckout');
      throw paymentError;
    }
  }

  /**
   * Get Chargily payment status and subscription details
   * Requires authentication
   */
  static async getChargilyPaymentStatus(): Promise<ChargilyPaymentStatusResponse> {
    try {
      const response = await apiClient.get<ChargilyPaymentStatusResponse>(
        config.endpoints.payment.chargilyStatus
      );
      return response.data;
    } catch (error) {
      const paymentError = parsePaymentError(error, 'chargily');
      logPaymentError(paymentError, 'getChargilyPaymentStatus');
      throw paymentError;
    }
  }

  /**
   * Create a payment link for Chargily Pay
   * Requires authentication
   */
  static async createPaymentLink(data: {
    name: string;
    description: string;
    planId: string;
    customAmount?: boolean;
    metadata?: Record<string, any>;
  }) {
    const response = await apiClient.post(
      config.endpoints.payment.chargilyPaymentLinks,
      data
    );
    return response.data;
  }

  /**
   * Get all payment links created by the provider
   * Requires authentication
   */
  static async getPaymentLinks() {
    const response = await apiClient.get(
      config.endpoints.payment.chargilyPaymentLinks
    );
    return response.data;
  }

  /**
   * Create a customer record in Chargily
   * Requires authentication
   */
  static async createChargilyCustomer(data: {
    name: string;
    email: string;
    phone: string;
    address?: {
      country: string;
      state: string;
      address: string;
    };
    metadata?: Record<string, any>;
  }) {
    const response = await apiClient.post(
      config.endpoints.payment.chargilyCustomer,
      data
    );
    return response.data;
  }

  /**
   * Get Chargily customer information
   * Requires authentication
   */
  static async getChargilyCustomer() {
    const response = await apiClient.get(
      config.endpoints.payment.chargilyCustomer
    );
    return response.data;
  }

  /**
   * Update Chargily customer information
   * Requires authentication
   */
  static async updateChargilyCustomer(data: {
    name?: string;
    email?: string;
    phone?: string;
    address?: {
      country: string;
      state: string;
      address: string;
    };
    metadata?: Record<string, any>;
  }) {
    const response = await apiClient.put(
      config.endpoints.payment.chargilyCustomer,
      data
    );
    return response.data;
  }

  /**
   * Determine the recommended payment method based on user location
   * Utility method for payment method selection
   */
  static getRecommendedPaymentMethod(
    userLocation: string,
    availableMethods: PaymentMethod[]
  ): PaymentMethod | null {
    return HardcodedPaymentMethodsService.getRecommendedPaymentMethod(userLocation);
  }

  /**
   * Filter available payment methods based on user location
   * Utility method for payment method availability
   */
  static getAvailablePaymentMethodsForLocation(
    userLocation: string,
    allMethods: PaymentMethod[]
  ): PaymentMethod[] {
    return HardcodedPaymentMethodsService.getPaymentMethodsForRegion(userLocation);
  }

  /**
   * Check if Chargily Pay is available for the user
   * Convenience method for Chargily availability
   */
  static async isChargilyAvailable(userCountryCode?: string): Promise<boolean> {
    return HardcodedPaymentMethodsService.isPaymentMethodAvailable('chargily', userCountryCode);
  }

  /**
   * Get the user's country/region for payment method selection
   * Convenience method for location-based recommendations
   */
  static async getUserRegion(userCountryCode?: string): Promise<string> {
    try {
      const response = await this.getAvailablePaymentMethods(userCountryCode);
      return response.data.providerCountry;
    } catch (error) {
      console.error('Error getting user region:', error);
      return '';
    }
  }

  /**
   * Validate payment method selection
   * Utility method for form validation
   */
  static validatePaymentMethodSelection(
    paymentProcessor: PaymentProcessor,
    paymentMethod: PaymentMethodType,
    userCountryCode?: string
  ): boolean {
    const validation = HardcodedPaymentMethodsService.validatePaymentMethodSelection(
      paymentProcessor,
      paymentMethod,
      userCountryCode
    );
    return validation.isValid;
  }

  /**
   * Format currency amount based on payment method
   * Utility method for currency display
   */
  static formatCurrencyForPaymentMethod(
    amount: number,
    paymentMethod: PaymentMethod,
    locale?: string
  ): string {
    // Import currency utilities dynamically to avoid circular dependencies
    const { formatCurrencyForPaymentMethod } = require('../utils/currency.utils');
    return formatCurrencyForPaymentMethod(amount, paymentMethod, { locale });
  }

  /**
   * Redirect to Chargily checkout URL
   * Utility method for seamless checkout flow
   */
  static redirectToChargilyCheckout(checkoutUrl: string): void {
    window.location.href = checkoutUrl;
  }

  /**
   * Get payment method by ID
   * Utility method to find specific payment method details
   */
  static getPaymentMethodById(methodId: string): PaymentMethod | null {
    return HardcodedPaymentMethodsService.getPaymentMethodById(methodId);
  }
}
