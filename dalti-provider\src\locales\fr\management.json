{"appointments": {"title": "Gestion des rendez-vous", "description": "<PERSON><PERSON>rez vos rendez-vous et réservations clients", "noAppointments": "Aucun rendez-vous trouvé", "noAppointmentsFiltered": "Aucun rendez-vous ne correspond à vos filtres actuels. Essayez d'ajuster vos critères de recherche.", "getStarted": "Commencez par créer votre premier rendez-vous.", "createFirstAppointment": "Créer votre premier rendez-vous", "failedToLoad": "Échec du chargement des rendez-vous", "details": {"title": "<PERSON><PERSON><PERSON> du rendez-vous", "subtitle": "Voir et gérer les informations du rendez-vous", "customerInformation": "Informations client", "serviceDetails": "Détails du service", "service": "Service", "minutes": "minutes", "schedule": "<PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON>", "cancelAppointment": "<PERSON><PERSON><PERSON> le rendez-vous", "cancelReasonPlaceholder": "Raison de l'annulation (optionnel)", "cancelling": "Annulation...", "confirmCancel": "Confirmer l'annulation", "completeAppointment": "<PERSON><PERSON><PERSON> le rendez-vous", "completionNotesPlaceholder": "Notes de fin (optionnel)", "completing": "Finalisation...", "markComplete": "Marquer comme terminé", "rescheduleAppointment": "Reporter le rendez-vous", "rescheduling": "Report...", "reschedule": "Reporter", "confirming": "Confirmation...", "starting": "Démarrage...", "start": "Commencer", "complete": "<PERSON><PERSON><PERSON>", "marking": "Marquage...", "noShow": "Absent", "appointmentStarted": "<PERSON><PERSON>-vous commencé"}}, "filters": {"all": "Tous", "active": "Actif", "inactive": "Inactif", "public": "Public", "private": "Priv<PERSON>", "clearFilters": "Effacer les filtres", "applyFilters": "Appliquer les filtres", "dateRange": "Plage de dates", "allDates": "Toutes les dates", "today": "<PERSON><PERSON><PERSON>'hui", "next7Days": "7 prochains jours", "next30Days": "30 prochains jours", "startDate": "Date de début", "endDate": "Date de fin", "category": "<PERSON><PERSON><PERSON><PERSON>", "allCategories": "Toutes les catégories", "deliveryType": "Type de livraison", "allTypes": "Tous les types", "atLocation": "Sur place", "atCustomer": "Chez le client", "both": "Les deux"}, "common": {"loading": "Chargement...", "error": "Erreur lors du chargement des données", "retry": "<PERSON><PERSON><PERSON><PERSON>", "search": "Rechercher...", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON>", "actions": "Actions", "status": "Statut", "active": "Actif", "inactive": "Inactif", "enabled": "Activé", "disabled": "Désactivé", "public": "Public", "private": "Priv<PERSON>", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "management": "Gestion"}, "customers": {"title": "Gestion des clients", "description": "<PERSON><PERSON>rez vos relations clients et suivez leur historique de rendez-vous", "addNewCustomer": "Ajouter un nouveau client", "editCustomer": "Modifier le client", "deleteCustomer": "Supprimer le client", "viewCustomer": "Voir le client", "noCustomers": "Aucun client trouvé", "noCustomersFound": "Aucun client trouvé", "noCustomersFiltered": "Aucun client ne correspond à vos filtres actuels. Essayez d'ajuster vos critères de recherche.", "getStarted": "Commencez par ajouter votre premier client pour établir des relations.", "addFirstCustomer": "Ajoutez votre premier client pour commencer", "failedToLoad": "Échec du chargement des clients", "customerStatistics": "Statistiques des clients", "totalCustomers": "Total des clients", "activeCustomers": "Clients actifs", "inactiveCustomers": "Clients inactifs", "newThisMonth": "Nouveaux ce mois", "avgAppointments": "<PERSON><PERSON><PERSON>vous moyens", "vsLastMonth": "par rapport au mois dernier", "customerDistribution": "Répartition des clients", "customerEngagement": "Engagement des clients", "totalAppointments": "Total des rendez-vous", "avgPerCustomer": "Moyenne par client", "retentionRate": "<PERSON>x de ré<PERSON>tion", "repeatCustomers": "Clients r<PERSON>s", "active": "Actif", "inactive": "Inactif", "searchPlaceholder": "Rechercher des clients...", "export": {"title": "Exporter les clients", "description": "Exportez vos données clients dans différents formats", "exportFormat": "Format d'exportation", "formats": {"csvDescription": "Valeurs séparées par des virgules", "jsonDescription": "Notation d'objet JavaScript", "excelDescription": "Format Microsoft Excel"}, "fieldsToExport": "Champs à exporter ({{count}} sélectionnés)", "selectAll": "<PERSON><PERSON>", "deselectAll": "<PERSON><PERSON>", "fieldLabels": {"firstName": "Prénom", "lastName": "Nom de famille", "mobileNumber": "Numéro de téléphone portable", "email": "Adresse e-mail", "nationalId": "Carte d'identité nationale", "notes": "Notes", "appointmentCount": "Nombre de rendez-vous", "createdAt": "Date de création", "isActive": "Actif"}, "fields": {"firstName": "Prénom", "lastName": "Nom de famille", "mobileNumber": "Numéro de téléphone portable", "email": "Adresse e-mail", "nationalId": "Carte d'identité nationale", "notes": "Notes", "appointmentCount": "Nombre de rendez-vous", "createdAt": "Date de création", "status": "Statut"}, "exportOptions": "Options d'exportation", "includeInactiveCustomers": "Inclure les clients inactifs", "readyToExport": "Prêt à exporter {{customerCount}} clients avec {{fieldCount}} champs", "exportButton": "Exporter {{format}}", "noCustomersToExport": "Aucun client à exporter", "exportFailed": "L'exportation a échoué. Veuillez réessayer."}, "addCustomer": "Ajouter un client", "quickFilters": "Filtres rapides", "allCustomers": "Tous les clients", "withEmail": "Avec e-mail", "highEngagement": "Engagement élevé", "customerSince": "Client depuis", "appointments": "<PERSON><PERSON><PERSON>vous", "appointmentCount": "Nombre de rendez-vous", "lastUpdated": "Dernière mise à jour", "view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Actions", "statistics": "Statistiques", "details": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "editCustomer": "Modifier le client", "customerSince": "Client depuis {{date}}", "contactInformation": "Informations de contact", "nationalId": "Carte d'identité nationale", "customerStatistics": "Statistiques du client", "totalAppointments": "Total des rendez-vous", "relationshipStatus": "Statut de la relation", "customerSinceLabel": "Client depuis", "providerNotes": "Notes du prestataire", "appointmentHistory": "Historique des rendez-vous", "appointmentHistoryDescription": "L'historique détaillé des rendez-vous sera affiché ici lors de l'intégration avec le système de rendez-vous.", "totalAppointmentsCount": "Total des rendez-vous : {{count}}", "providerNotesCommunications": "Notes du prestataire et communications", "customerNotes": "Notes du client", "lastUpdated": "<PERSON><PERSON><PERSON> mise à jour : {{date}}", "noNotesYet": "Aucune note pour le moment", "addNotesDescription": "Ajoutez des notes sur ce client pour garder une trace des informations importantes.", "addNotes": "Ajouter des notes"}}, "services": {"title": "Gestion des services", "subtitle": "Créez et gérez vos offres de services", "description": "G<PERSON>rez vos services, tarifs et catégories", "manageCategories": "<PERSON><PERSON><PERSON> les catégories", "createService": "Créer un service", "yourServices": "Vos services", "manageOfferings": "G<PERSON>rez vos offres de services et tarifs", "search": "<PERSON><PERSON><PERSON>", "status": "Statut", "searchPlaceholder": "Rechercher des services...", "allServices": "Tous les services", "public": "Public", "private": "Priv<PERSON>", "noServicesFound": "Aucun service trouvé", "noServicesFiltered": "Aucun service ne correspond à vos filtres actuels. Essayez d'ajuster vos critères de recherche.", "getStarted": "Commencez par créer votre premier service.", "createFirstService": "Créer votre premier service", "failedToLoad": "Échec du chargement des services", "categoryManager": {"title": "Gérer les catégories de services", "subtitle": "Créez et organisez vos catégories de services", "createCategory": "<PERSON><PERSON>er une catégorie", "editCategory": "Modifier la catégorie", "backToList": "Retour à la liste", "categoryName": "Nom de la catégorie", "categoryNamePlaceholder": "Entrez le nom de la catégorie", "createFirstCategory": "Créer la première catégorie", "noCategoriesFound": "Aucune catégorie trouvée. Créez votre première catégorie pour organiser vos services.", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteConfirmation": "Êtes-vous sûr de vouloir supprimer cette catégorie ?", "categoryNameRequired": "Le nom de la catégorie doit contenir au moins 2 caractères", "loading": "Chargement des catégories...", "creating": "Création...", "updating": "Mise à jour...", "deleting": "Suppression..."}, "form": {"createNewService": "Créer un nouveau service", "editService": "Modifier le service", "addNewServiceDescription": "Ajoutez un nouveau service à vos offres", "updateServiceDescription": "Mettre à jour les détails du service", "serviceName": "Nom du service", "duration": "<PERSON><PERSON><PERSON> (minutes)", "price": "Prix", "pointsRequired": "Points requis", "deliveryType": "Type de livraison", "category": "<PERSON><PERSON><PERSON><PERSON>", "color": "<PERSON><PERSON><PERSON>", "description": "Description", "servedRegions": "Régions desservies", "serviceSettings": "Paramètres du service", "placeholders": {"serviceName": "Entrez le nom du service", "duration": "60", "price": "0,00", "points": "0", "description": "Décrivez votre service...", "regions": "Entrez les régions séparées par des virgules (ex: Centre-ville, Banlieue)"}, "deliveryOptions": {"atLocation": "Sur place", "atCustomer": "Chez le client", "both": "Les deux"}, "noCategory": "Aucune catégorie", "colors": {"blue": "Bleu", "green": "<PERSON>ert", "yellow": "Jaune", "red": "Rouge", "purple": "Violet", "cyan": "<PERSON><PERSON>", "orange": "Orange", "lime": "Citron vert"}, "settings": {"publicService": "Service public", "publicServiceDesc": "Visible par les clients", "onlineBooking": "Réservation en ligne", "onlineBookingDesc": "Autoriser les rendez-vous en ligne", "acceptNewCustomers": "Accepter de nouveaux clients", "acceptNewCustomersDesc": "Ouvert aux nouveaux clients", "notifications": "Notifications", "notificationsDesc": "Recevoir les alertes de réservation"}, "helpText": {"pointsTooltip": "Crédits/points dont les clients ont besoin pour réserver ce service. <PERSON><PERSON> <PERSON><PERSON><PERSON>, 1 point par réservation.", "regionsHelp": "Laissez vide si le service est disponible partout"}, "createService": "Créer un service", "updateService": "Mettre à jour le service"}, "deleteConfirmTitle": "Supprimer le service", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer ce service ? Cette action ne peut pas être annulée et supprimera toutes les données associées."}, "locations": {"title": "Gestion des lieux", "subtitle": "Configurez vos emplacements d'entreprise et heures d'ouverture", "description": "<PERSON><PERSON><PERSON> vos lieux de service et horaires d'ouverture", "loadingLocations": "Chargement des emplacements...", "yourLocations": "Vos emplacements", "manageLocations": "<PERSON><PERSON><PERSON> vos emplacements d'entreprise et heures d'ouverture", "createLocation": "<PERSON><PERSON><PERSON> un lieu", "editLocation": "Modifier le lieu", "deleteLocation": "Supprimer le lieu", "manageHours": "<PERSON><PERSON><PERSON> les horaires", "addLocation": "Ajouter un lieu", "city": "Ville", "amenities": "Équipements", "search": "<PERSON><PERSON><PERSON>", "cityPlaceholder": "Filtrer par ville...", "searchPlaceholder": "Rechercher des lieux...", "allLocations": "<PERSON><PERSON> les lieux", "withParking": "Avec parking", "withElevator": "Avec ascenseur", "handicapAccessible": "Accessible aux handicapés", "noLocationsFound": "Aucun lieu trouvé", "noLocationsFiltered": "Aucun lieu ne correspond à vos filtres actuels. Essayez d'ajuster vos critères de recherche.", "getStarted": "Commencez par ajouter votre premier lieu d'affaires.", "addFirstLocation": "Ajouter votre premier lieu", "failedToLoad": "Échec du chargement des lieux", "noLocations": "Aucun lieu trouvé", "form": {"createLocation": "<PERSON><PERSON><PERSON> un lieu", "editLocation": "Modifier le lieu", "addNewLocationDescription": "Ajouter un nouveau lieu de service", "updateLocationDescription": "Mettre à jour les détails du lieu", "basicInformation": "Informations de base", "addressInformation": "Informations d'adresse", "contactInformation": "Informations de contact", "amenitiesAccessibility": "Équipements et accessibilité", "coordinates": "Coordonnées", "openingHours": "Heures d'ouverture", "locationName": "Nom du lieu", "shortName": "Nom court", "streetAddress": "Adresse de rue", "city": "Ville", "country": "Pays", "postalCode": "Code postal", "floorSuite": "Étage/Suite", "timezone": "<PERSON><PERSON> ho<PERSON>", "mobilePhone": "Téléphone mobile", "fax": "Fax", "latitude": "Latitude", "longitude": "Longitude", "dayOfWeek": "<PERSON><PERSON> <PERSON>", "placeholders": {"enterName": "Entrez le nom du lieu", "enterShortName": "Nom court optionnel", "enterAddress": "Entrez l'adresse de rue", "enterCity": "Entrez la ville", "enterCountry": "Entrez le pays", "enterPostalCode": "Entrez le code postal", "floorNumber": "Numéro d'étage ou de suite", "enterMobile": "Entrez le numéro de mobile", "enterFax": "Entrez le numéro de fax", "latitudeExample": "ex: 40.7128", "longitudeExample": "ex: -74.0060"}, "buttons": {"useMyTimezone": "Utiliser mon fuseau horaire", "selectTimezone": "Sélectionner le fuseau horaire", "standardHours": "Heures standard", "addDay": "Ajouter un jour", "useCurrentLocation": "Utiliser ma position actuelle", "gettingLocation": "Obtention de la position...", "createLocation": "<PERSON><PERSON><PERSON> un lieu", "updateLocation": "Mettre à jour le lieu", "creating": "Création...", "updating": "Mise à jour..."}, "checkboxes": {"hideMobile": "Masquer le numéro de mobile aux clients", "hideMobileDesc": "Le numéro de mobile ne sera pas affiché publiquement", "parkingAvailable": "Parking disponible", "parkingDesc": "Parking sur site pour les clients", "elevatorAccess": "Accès par ascenseur", "elevatorDesc": "Ascenseur disponible dans le bâtiment", "handicapAccessible": "Accessible aux handicapés", "handicapDesc": "Entrée accessible en fauteuil roulant"}, "helpText": {"coordinatesHelp": "Les coordonnées aident les clients à trouver votre lieu plus facilement sur les cartes", "noOpeningHours": "Aucune heure d'ouverture définie. Cliquez sur \"Ajouter un jour\" pour commencer."}, "daysOfWeek": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>"}}, "deleteConfirmTitle": "Supprimer l'emplacement", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer cet emplacement ? Cette action ne peut pas être annulée."}, "queues": {"title": "Gestion des files d'attente", "subtitle": "Configurez les files d'attente de rendez-vous et systèmes d'attente", "description": "<PERSON><PERSON><PERSON> vos files d'attente de service et listes d'attente", "createQueue": "<PERSON><PERSON><PERSON> une file d'attente", "editQueue": "Modifier la file d'attente", "deleteQueue": "Supprimer la file d'attente", "viewQueue": "Voir la file d'attente", "loadingQueues": "Chargement des files d'attente...", "yourQueues": "Vos files d'attente", "manageQueues": "<PERSON><PERSON><PERSON> vos files d'attente de rendez-vous et systèmes d'attente", "noQueues": "Aucune file d'attente trouvée", "addFirstQueue": "Ajoutez votre première file d'attente pour commencer", "queueStatistics": "Statistiques des files d'attente", "activeQueues": "Files d'attente actives", "totalWaiting": "Total en attente", "averageWaitTime": "Temps d'attente moyen", "failedToLoad": "Échec du chargement des files d'attente", "noQueuesFound": "Aucune file d'attente trouvée", "noQueuesFiltered": "Aucune file d'attente ne correspond à vos filtres actuels. Essayez d'ajuster vos critères de recherche.", "getStarted": "Commencez par créer votre première file d'attente pour gérer les temps d'attente des clients.", "createFirstQueue": "<PERSON><PERSON><PERSON> votre première file d'attente", "status": "Statut", "location": "Emplacement", "service": "Service", "search": "<PERSON><PERSON><PERSON>", "allQueues": "Toutes les files d'attente", "active": "Actif", "inactive": "Inactif", "allLocations": "Tous les emplacements", "allServices": "Tous les services", "searchPlaceholder": "Rechercher des files d'attente...", "deleteConfirmTitle": "Supprimer la file d'attente", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer cette file d'attente ? Cette action ne peut pas être annulée et supprimera toutes les données associées.", "deleteConfirmButton": "Supprimer la file d'attente", "cancelButton": "Annuler", "queueLimits": "Limites des files d'attente", "queuesUsed": "Files d'attente utilisées", "currentPlan": "Plan actuel", "planFeatures": "Fonctionnalités du plan", "realTimeUpdates": "Mises à jour en temps réel", "advancedAnalytics": "Analyses avancées", "prioritySupport": "Support prioritaire", "queueLimitReached": "Limite de file d'attente atteinte", "upgradeToCreateMore": "Mettez à niveau votre plan pour créer plus de files d'attente et débloquer des fonctionnalités supplémentaires.", "readyToCreate": "<PERSON><PERSON><PERSON><PERSON>", "canCreateMore": "<PERSON><PERSON> pouvez créer {count} file(s) d'attente supplémentaire(s).", "unlimited": "illimité", "free": "<PERSON><PERSON><PERSON>", "queueDetails": {"activeSession": "Session active", "inProgress": "En cours", "customer": "Client", "service": "Service", "timeRemaining": "Temps restant", "started": "<PERSON><PERSON><PERSON><PERSON>", "noActiveSession": "Aucune session active", "noActiveSessionDesc": "Aucun rendez-vous n'est actuellement en cours pour cette file d'attente", "queueStats": "Statistiques de la file d'attente", "confirmedAppointments": "<PERSON><PERSON><PERSON><PERSON>ous <PERSON>", "upcomingToday": "À venir aujourd'hui", "upcomingAppointments": "<PERSON>dez-vous à venir", "next5": "5 suivants", "noUpcomingAppointments": "Aucun rendez-vous à venir pour cette file d'attente"}, "form": {"createQueue": "<PERSON><PERSON><PERSON> une file d'attente", "editQueue": "Modifier la file d'attente", "createNewQueueDescription": "<PERSON><PERSON>er une nouvelle file d'attente pour gérer les temps d'attente des clients", "updateQueueDescription": "Mettre à jour les détails de la file d'attente", "queueTitle": "Titre de la file d'attente", "location": "Emplacement", "services": "Services", "openingHours": "Heures d'ouverture", "queueTitlePlaceholder": "Entrez le titre de la file d'attente", "selectLocation": "Sélectionner un emplacement", "standardHours": "Heures standard", "addDay": "Ajouter un jour", "remove": "<PERSON><PERSON><PERSON><PERSON>", "addTimeSlot": "Ajouter un créneau", "cancel": "Annuler", "createQueueBtn": "<PERSON><PERSON><PERSON> une file d'attente", "updateQueueBtn": "Mettre à jour la file d'attente", "submitting": "Envoi en cours...", "queueIsActive": "File d'attente active", "active": "Actif", "to": "à", "noOpeningHours": "Aucune heure d'ouverture définie. Cliquez sur \"Ajouter un jour\" pour commencer.", "daysOfWeek": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>"}}, "cards": {"queue": {"active": "Actif", "inactive": "Inactif", "highPriority": "Priorité élevée", "mediumPriority": "Prior<PERSON> moyenne", "lowPriority": "Priorité faible", "current": "Actuel", "estimatedWait": "Attente est. (min)", "capacity": "Capacité", "services": "Services ({count})", "service": "Service {index}", "moreServices": "+{count} de plus", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "activateQueue": "Activer la file d'attente", "deactivateQueue": "Désactiver la file d'attente", "updated": "Mis à jour {date}", "maxCapacity": "/ {max} max"}, "location": {"active": "Actif", "noAddress": "<PERSON><PERSON>ne adresse sp<PERSON>", "floor": "Étage: {floor}", "fax": "Fax: {fax}", "amenities": "Commodités", "parking": "Parking", "elevator": "Ascenseur", "handicapAccess": "Accès handicapés", "edit": "Modifier", "hours": "<PERSON><PERSON>", "deleteLocation": "Supprimer l'emplacement", "deleting": "Suppression..."}}, "profile": {"title": "Profil", "businessProfile": "Profil d'entreprise", "accountInformation": "Informations du compte", "profilePicture": "Photo de profil", "businessLogo": "Logo de l'entreprise", "businessName": "Nom de l'entreprise", "phoneNumber": "Numéro de téléphone", "businessDescription": "Description de l'entreprise", "email": "Email", "accountName": "Nom du compte", "businessCategory": "Catégorie d'entreprise", "businessNamePlaceholder": "Entrez le nom de votre entreprise", "phoneNumberPlaceholder": "Entrez votre numéro de téléphone", "businessDescriptionPlaceholder": "Parlez de votre entreprise aux clients...", "editProfile": "Modifier le profil", "cancel": "Annuler", "uploadPicture": "Télécharger une photo", "uploadLogo": "Télécharger un logo", "removePicture": "Supprimer la photo", "removeLogo": "Supprimer le logo", "saveChanges": "Enregistrer les modifications", "uploading": "Téléchargement...", "removing": "Suppression...", "saving": "Enregistrement...", "noCategorySelected": "Aucune catégorie s<PERSON>ée", "completion": {"completeProfileSetup": "Complétez la configuration de votre profil", "profileSectionsComplete": "Sections du profil : {percentage}% complètes", "showLess": "Affiche<PERSON> moins", "showDetails": "Aff<PERSON>r les détails", "profileProgress": "Progression du profil", "uploadBusinessLogo": "Télécharger le logo de l'entreprise", "completeBusinessInfo": "Compléter les informations de l'entreprise", "addLogoDescription": "Ajoutez un logo professionnel pour établir la confiance avec les clients", "missingFields": "Manquant : {fields}", "uploadLogo": "Télécharger le logo", "completeInfo": "Compléter les infos", "overallSetup": "Configuration générale : {percentage}% complète", "continueSetup": "Continuer la configuration →", "profileSectionsCompleted": "Sections du profil complètes !", "continueOtherSections": "Continuer avec les autres sections →"}}, "subscription": {"title": "Abonnement", "management": "Gestion des abonnements", "description": "<PERSON><PERSON>rez votre abonnement, consultez l'utilisation et mettez à niveau votre plan", "upgradePlan": "Mettre à niveau le plan", "changePlan": "Changer de plan", "overview": {"usageStatistics": "Statistiques d'utilisation", "thisMonth": "Ce mois-ci", "creditsUsed": "Crédits utilisés", "of": "sur", "percentUsed": "{percentage}% utilisé", "remaining": "{remaining} restant", "queuesActive": "Files d'attente actives", "available": "{available} disponible", "currentPlan": "Plan actuel :", "planName": "Nom du plan", "price": "Prix", "credits": "Crédits", "queues": "Files d'attente", "perMonth": "/mois", "unlimited": "Illimité", "features": "Fonctionnalités", "current": "Actuel", "recommended": "Recommandé", "mostPopular": "Le plus populaire", "subscriptionManagement": "Gestion des abonnements", "portalUnavailable": "Portail indisponible", "manageSubscription": "<PERSON><PERSON>rer l'abonnement", "openingPortal": "Ouverture du portail...", "loading": "Chargement...", "manageBilling": "<PERSON><PERSON><PERSON> la facturation", "portalAccessMessage": "L'accès au portail client nécessite un abonnement actif. Mettez à niveau votre plan pour accéder aux fonctionnalités de gestion des abonnements.", "portalDescriptionActive": "Accédez à votre portail client pour gérer votre {plan}, mettre à jour les méthodes de paiement et consulter l'historique de facturation.", "creditsDisplay": "{amount} Crédits", "plans": {"oneTimePurchaseDescription": "Achat unique de {amount} crédits pour votre compte", "mostPopularPlan": "Notre plan le plus populaire", "allYouNeedToStart": "Tout ce dont vous avez besoin pour commencer", "freePlanForEveryone": "Plan gratuit pour tous", "priorityCustomerSupport": "Support client prioritaire", "basicSupport": "Support de base", "noExpirationDate": "Aucune date d'expiration", "queues": "Files d'attente {count}", "queue": "File d'attente {count}", "creditsPerMonth": "{amount} Crédits/mois", "oneTimePurchase": "<PERSON><PERSON><PERSON> unique", "monthlySubscription": "Abonnement mensuel", "needHelpChoosing": "Besoin d'aide pour choisir ?", "planFeaturesDescription": "Tous les plans incluent nos fonctionnalités principales. <PERSON><PERSON> pouvez mettre à niveau ou rétrograder à tout moment, et les crédits inutilisés n'expirent jamais."}}, "plans": "Plans", "usage": "Utilisation", "failedToLoad": "Échec du chargement des données d'abonnement", "currentPlanDetails": "Détails du plan actuel", "usageInsights": "Aperçus d'utilisation", "choosePlan": "Choisissez votre plan", "planDescription": "Sélectionnez le plan parfait pour les besoins de votre entreprise. Mettez à niveau ou rétrogradez à tout moment.", "selectPlanDescription": "Sélectionnez le plan qui correspond le mieux aux besoins de votre entreprise", "currentPlan": "Plan actuel", "purchaseCredits": "Acheter des crédits", "getStarted": "Commencer", "subscribe": "<PERSON>'abonner"}}, "confirmations": {"deleteService": "Êtes-vous sûr de vouloir supprimer ce service ?", "deleteLocation": "Êtes-vous sûr de vouloir supprimer ce lieu ?", "deleteCustomer": "Êtes-vous sûr de vouloir supprimer ce client ?", "deleteQueue": "Êtes-vous sûr de vouloir supprimer cette file d'attente ?", "deleteWarning": "Cette action ne peut pas être annulée."}, "pagination": {"showing": "Affichage", "to": "à", "of": "sur", "page": "Page"}, "businessSetup": {"title": "Configuration d'entreprise", "subtitle": "Configurez vos services d'entreprise, emplacements et files d'attente de rendez-vous", "description": "Configurez vos services d'entreprise, emplacements et files d'attente de rendez-vous", "servicesDesc": "G<PERSON>rez vos offres de services et tarifs", "locationsDesc": "Configurez vos emplacements d'entreprise et heures d'ouverture", "queuesDesc": "Configurez les files d'attente de rendez-vous et systèmes d'attente"}, "serviceSession": {"title": "Session de Service", "description": "Gestion de session de service active", "sessionTimer": "Minuteur de Session", "currentCustomer": "Client Actuel", "comingNext": "Suivant", "extendTimer": "<PERSON><PERSON><PERSON> le Minuteur", "completeSession": "<PERSON><PERSON><PERSON>", "completing": "Finalisation...", "appointmentNotFound": "<PERSON><PERSON>-vous In<PERSON>uvable", "sessionNotActive": "Session Non Active", "couldNotLoad": "La session de rendez-vous n'a pas pu être chargée.", "notInProgress": "Ce rendez-vous n'est pas actuellement en cours.", "completedFromSessionPage": "Session terminée depuis la page de session de service", "sessionCompletedSuccessfully": "Session terminée avec succès !", "timerExtended": "Minuteur prolongé de {{minutes}} minutes", "noUpcomingAppointments": "Aucun rendez-vous à venir", "extendOptions": {"5minutes": "5 minutes", "10minutes": "10 minutes", "15minutes": "15 minutes", "30minutes": "30 minutes"}, "durationMinutes": "{{duration}} minutes"}}