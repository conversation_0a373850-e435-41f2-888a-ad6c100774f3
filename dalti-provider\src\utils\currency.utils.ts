/**
 * Currency Formatting Utilities
 * Handles currency formatting for different locales and payment methods
 */

import { PaymentMethod } from '../types';

/**
 * Supported currencies
 */
export type SupportedCurrency = 'DZD' | 'EUR';

/**
 * Currency configuration
 */
interface CurrencyConfig {
  code: string;
  symbol: string;
  name: string;
  locale: string;
  decimals: number;
  symbolPosition: 'before' | 'after';
  spaceBetween: boolean;
}

/**
 * Currency configurations
 */
const CURRENCY_CONFIGS: Record<SupportedCurrency, CurrencyConfig> = {
  DZD: {
    code: 'DZD',
    symbol: 'د.ج',
    name: 'Algerian Dinar',
    locale: 'ar-DZ',
    decimals: 0, // Algerian Dinar typically doesn't use decimals
    symbolPosition: 'after',
    spaceBetween: true,
  },
  EUR: {
    code: 'EUR',
    symbol: '€',
    name: 'Euro',
    locale: 'en-EU',
    decimals: 2,
    symbolPosition: 'after',
    spaceBetween: true,
  },
};

/**
 * Format currency amount with proper locale and formatting
 */
export function formatCurrency(
  amount: number,
  currency: SupportedCurrency,
  options?: {
    locale?: string;
    showSymbol?: boolean;
    showCode?: boolean;
    compact?: boolean;
  }
): string {
  const config = CURRENCY_CONFIGS[currency];
  const locale = options?.locale || config.locale;
  const showSymbol = options?.showSymbol !== false;
  const showCode = options?.showCode || false;
  const compact = options?.compact || false;

  try {
    // Use Intl.NumberFormat for proper localization
    const formatter = new Intl.NumberFormat(locale, {
      style: showSymbol ? 'currency' : 'decimal',
      currency: currency,
      minimumFractionDigits: config.decimals,
      maximumFractionDigits: config.decimals,
      notation: compact ? 'compact' : 'standard',
    });

    let formatted = formatter.format(amount);

    // Add currency code if requested
    if (showCode && !compact) {
      formatted = `${formatted} ${currency}`;
    }

    return formatted;
  } catch (error) {
    console.warn('Currency formatting failed, using fallback:', error);
    return formatCurrencyFallback(amount, currency, config);
  }
}

/**
 * Fallback currency formatting when Intl.NumberFormat fails
 */
function formatCurrencyFallback(
  amount: number,
  currency: SupportedCurrency,
  config: CurrencyConfig
): string {
  const formattedAmount = config.decimals > 0 
    ? amount.toFixed(config.decimals)
    : Math.round(amount).toString();

  const space = config.spaceBetween ? ' ' : '';

  if (config.symbolPosition === 'before') {
    return `${config.symbol}${space}${formattedAmount}`;
  } else {
    return `${formattedAmount}${space}${config.symbol}`;
  }
}

/**
 * Format currency for payment method display
 */
export function formatCurrencyForPaymentMethod(
  amount: number,
  paymentMethod: PaymentMethod,
  options?: {
    locale?: string;
    compact?: boolean;
  }
): string {
  const currency = paymentMethod.currency as SupportedCurrency;
  
  // Use payment method specific locale if available
  const locale = options?.locale || getLocaleForPaymentMethod(paymentMethod);
  
  return formatCurrency(amount, currency, {
    ...options,
    locale,
    showSymbol: true,
  });
}

/**
 * Get appropriate locale for payment method
 */
function getLocaleForPaymentMethod(paymentMethod: PaymentMethod): string {
  if (paymentMethod.id === 'chargily') {
    return 'ar-DZ'; // Arabic (Algeria)
  }
  
  if (paymentMethod.currency === 'DZD') {
    return 'ar-DZ';
  }
  
  return 'en-US'; // Default to English (US)
}

/**
 * Convert currency amounts (placeholder for future implementation)
 */
export async function convertCurrency(
  amount: number,
  fromCurrency: SupportedCurrency,
  toCurrency: SupportedCurrency
): Promise<number> {
  // This is a placeholder. In a real implementation, you would:
  // 1. Call a currency conversion API
  // 2. Cache exchange rates
  // 3. Handle rate updates
  
  if (fromCurrency === toCurrency) {
    return amount;
  }
  
  // Mock conversion rates (DO NOT use in production)
  const mockRates: Record<string, number> = {
    'USD-DZD': 134.5,
    'DZD-USD': 1 / 134.5,
    'EUR-DZD': 145.2,
    'DZD-EUR': 1 / 145.2,
    'USD-EUR': 0.85,
    'EUR-USD': 1.18,
  };
  
  const rateKey = `${fromCurrency}-${toCurrency}`;
  const rate = mockRates[rateKey];
  
  if (rate) {
    return amount * rate;
  }
  
  console.warn(`No conversion rate available for ${fromCurrency} to ${toCurrency}`);
  return amount;
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currency: SupportedCurrency): string {
  return CURRENCY_CONFIGS[currency]?.symbol || currency;
}

/**
 * Get currency name
 */
export function getCurrencyName(currency: SupportedCurrency): string {
  return CURRENCY_CONFIGS[currency]?.name || currency;
}

/**
 * Check if currency uses decimals
 */
export function currencyUsesDecimals(currency: SupportedCurrency): boolean {
  return CURRENCY_CONFIGS[currency]?.decimals > 0;
}

/**
 * Format price range
 */
export function formatPriceRange(
  minAmount: number,
  maxAmount: number,
  currency: SupportedCurrency,
  options?: {
    locale?: string;
    compact?: boolean;
  }
): string {
  const minFormatted = formatCurrency(minAmount, currency, options);
  const maxFormatted = formatCurrency(maxAmount, currency, options);
  
  return `${minFormatted} - ${maxFormatted}`;
}

/**
 * Format currency with custom precision
 */
export function formatCurrencyWithPrecision(
  amount: number,
  currency: SupportedCurrency,
  precision: number,
  options?: {
    locale?: string;
    showSymbol?: boolean;
  }
): string {
  const config = CURRENCY_CONFIGS[currency];
  const locale = options?.locale || config.locale;
  const showSymbol = options?.showSymbol !== false;

  try {
    const formatter = new Intl.NumberFormat(locale, {
      style: showSymbol ? 'currency' : 'decimal',
      currency: currency,
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    });

    return formatter.format(amount);
  } catch (error) {
    console.warn('Currency formatting failed, using fallback:', error);
    const formattedAmount = amount.toFixed(precision);
    const space = config.spaceBetween ? ' ' : '';
    
    if (!showSymbol) {
      return formattedAmount;
    }
    
    if (config.symbolPosition === 'before') {
      return `${config.symbol}${space}${formattedAmount}`;
    } else {
      return `${formattedAmount}${space}${config.symbol}`;
    }
  }
}

/**
 * Parse currency string to number
 */
export function parseCurrencyString(
  currencyString: string,
  currency: SupportedCurrency
): number | null {
  const config = CURRENCY_CONFIGS[currency];
  
  // Remove currency symbols and spaces
  let cleanString = currencyString
    .replace(config.symbol, '')
    .replace(/\s/g, '')
    .replace(currency, '');
  
  // Handle different decimal separators
  if (config.locale.includes('ar')) {
    // Arabic locales might use different separators
    cleanString = cleanString.replace(/[٠-٩]/g, (d) => '٠١٢٣٤٥٦٧٨٩'.indexOf(d).toString());
  }
  
  const parsed = parseFloat(cleanString);
  return isNaN(parsed) ? null : parsed;
}

/**
 * Get currency formatting info
 */
export function getCurrencyInfo(currency: SupportedCurrency): CurrencyConfig {
  return CURRENCY_CONFIGS[currency];
}

/**
 * Format currency for display in tables or lists
 */
export function formatCurrencyCompact(
  amount: number,
  currency: SupportedCurrency
): string {
  return formatCurrency(amount, currency, {
    compact: true,
    showSymbol: true,
  });
}
