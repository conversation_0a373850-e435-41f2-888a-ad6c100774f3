import React, { useState } from 'react';
import { useCurrency } from '../context/CurrencyContext';
import { SupportedCurrency, formatCurrency, getCurrencySymbol } from '../utils/currency.utils';
import { formatSubscriptionPrice, extractPriceFromString } from '../utils/subscription-currency.utils';
import CurrencySelector, { CompactCurrencySelector, SidebarCurrencySelector } from '../components/ui/CurrencySelector';
import PageMeta from '../components/common/PageMeta';

const TestCurrency: React.FC = () => {
  const { defaultCurrency, currencySymbol, formatPrice, setDefaultCurrency } = useCurrency();
  const [testAmount, setTestAmount] = useState(99.99);
  const [testResults, setTestResults] = useState<string[]>([]);

  // Sample subscription plans for testing
  const samplePlans = [
    { id: 'free', name: 'Free Plan', price: 'Free', originalPrice: 'Free' },
    { id: 'basic', name: 'Basic Plan', price: '€19.99', originalPrice: '€19.99' },
    { id: 'pro', name: 'Pro Plan', price: 'د.ج 2500', originalPrice: 'د.ج 2500' },
    { id: 'premium', name: 'Premium Plan', price: '€49.99', originalPrice: '€49.99' },
    { id: 'enterprise', name: 'Enterprise Plan', price: 'د.ج 8000', originalPrice: 'د.ج 8000' },
  ];

  const addTestResult = (result: string) => {
    setTestResults(prev => [`${new Date().toLocaleTimeString()}: ${result}`, ...prev.slice(0, 9)]);
  };

  const testCurrencyChange = async (currency: SupportedCurrency) => {
    addTestResult(`🔄 Testing currency change to ${currency}`);
    try {
      await setDefaultCurrency(currency);
      addTestResult(`✅ Currency changed successfully to ${currency} (${getCurrencySymbol(currency)})`);
    } catch (error) {
      addTestResult(`❌ Failed to change currency: ${error}`);
    }
  };

  const testPriceFormatting = () => {
    addTestResult(`🧪 Testing price formatting for ${testAmount}`);

    const currencies: SupportedCurrency[] = ['DZD', 'EUR'];
    currencies.forEach(currency => {
      const formatted = formatCurrency(testAmount, currency);
      addTestResult(`${currency}: ${formatted}`);
    });
  };

  const testSubscriptionPriceConversion = () => {
    addTestResult(`🧪 Testing subscription price conversion to ${defaultCurrency}`);
    
    samplePlans.forEach(plan => {
      const converted = formatSubscriptionPrice(plan.originalPrice, defaultCurrency);
      const extracted = extractPriceFromString(plan.originalPrice);
      addTestResult(`${plan.name}: ${plan.originalPrice} → ${converted} (extracted: ${extracted})`);
    });
  };

  const testPriceExtraction = () => {
    addTestResult(`🧪 Testing price extraction from strings`);

    const testPrices = ['€19.99', 'Free', 'د.ج 5000', 'Gratuit', 'مجاني', '€0', 'د.ج 1500'];
    testPrices.forEach(price => {
      const extracted = extractPriceFromString(price);
      addTestResult(`"${price}" → ${extracted}`);
    });
  };

  return (
    <div className="p-6 space-y-6">
      <PageMeta
        title="Currency Test - Dalti Provider Dashboard"
        description="Testing dynamic currency functionality"
      />

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Currency System Test
        </h1>

        {/* Current Currency Info */}
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Current Currency: {defaultCurrency} ({currencySymbol})
          </h2>
          <p className="text-blue-700 dark:text-blue-300">
            Test amount: {formatPrice(testAmount)}
          </p>
        </div>

        {/* Currency Selector */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Currency Selector Components
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Standalone Selector</h4>
              <CurrencySelector variant="standalone" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Header Style (Compact)</h4>
              <CompactCurrencySelector className="inline-block" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sidebar Style</h4>
              <SidebarCurrencySelector />
            </div>
          </div>
        </div>

        {/* Test Amount Input */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Test Amount
          </h3>
          <div className="flex items-center space-x-3">
            <input
              type="number"
              value={testAmount}
              onChange={(e) => setTestAmount(parseFloat(e.target.value) || 0)}
              step="0.01"
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              placeholder="Enter test amount"
            />
            <span className="text-gray-600 dark:text-gray-400">
              = {formatPrice(testAmount)}
            </span>
          </div>
        </div>

        {/* Currency Change Tests */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Test Currency Changes
          </h3>
          <div className="flex flex-wrap gap-3">
            {(['DZD', 'EUR'] as SupportedCurrency[]).map((currency) => (
              <button
                key={currency}
                onClick={() => testCurrencyChange(currency)}
                disabled={defaultCurrency === currency}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  defaultCurrency === currency
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {currency} ({getCurrencySymbol(currency)})
              </button>
            ))}
          </div>
        </div>

        {/* Manual Tests */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Manual Tests
          </h3>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={testPriceFormatting}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Test Price Formatting
            </button>
            <button
              onClick={testSubscriptionPriceConversion}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700"
            >
              Test Subscription Conversion
            </button>
            <button
              onClick={testPriceExtraction}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
            >
              Test Price Extraction
            </button>
          </div>
        </div>

        {/* Sample Subscription Plans */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Sample Subscription Plans (Converted to {defaultCurrency})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {samplePlans.map((plan) => {
              const convertedPrice = formatSubscriptionPrice(plan.originalPrice, defaultCurrency);
              return (
                <div key={plan.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white">{plan.name}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Original: {plan.originalPrice}
                  </p>
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    Converted: {convertedPrice}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Test Results */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Test Results
          </h3>
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 max-h-64 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 italic">
                No test results yet. Try changing currency or running manual tests.
              </p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono text-gray-700 dark:text-gray-300">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestCurrency;
