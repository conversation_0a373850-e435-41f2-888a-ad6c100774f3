import React from 'react';
import { useNavigate } from 'react-router';
import Button from '../ui/button/Button';
import { formatLocalTime } from '../../utils/timezone';
import { useAppointments } from '../../hooks/useAppointments';
import { useDashboardTranslation } from '../../hooks/useTranslation';

const RecentAppointmentsSimple: React.FC = () => {
  const navigate = useNavigate();
  const { data: allAppointments, isLoading, error } = useAppointments();
  const { t } = useDashboardTranslation();

  // Filter for recent completed appointments (last 5)
  const recentAppointments = React.useMemo(() => {
    if (!allAppointments) return [];

    return allAppointments
      .filter(apt => apt.status === 'completed')
      .sort((a, b) => new Date(b.expectedAppointmentStartTime).getTime() - new Date(a.expectedAppointmentStartTime).getTime())
      .slice(0, 5);
  }, [allAppointments]);

  const totalRevenue = recentAppointments
    .reduce((sum, apt) => sum + (apt.service?.price || 0), 0);

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 animate-pulse">
        <div className="flex items-center justify-between mb-6">
          <div>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
        </div>
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                <div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-24 mb-1"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-16"></div>
                </div>
              </div>
              <div className="text-right">
                <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-12 mb-1"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('recentAppointments.errorTitle', 'Error Loading Appointments')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('recentAppointments.errorMessage', 'Unable to load recent appointments. Please try again.')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('recentAppointments.title', 'Recent Appointments')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            ${totalRevenue.toLocaleString()} {t('recentAppointments.fromCompleted', 'from')} {recentAppointments.length} {t('recentAppointments.completedAppointments', 'completed appointments')}
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/appointments')}
        >
          {t('recentAppointments.viewAll', 'View All')}
        </Button>
      </div>

      <div className="space-y-3">
        {recentAppointments.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-400">{t('recentAppointments.noRecent', 'No recent appointments')}</p>
          </div>
        ) : (
          recentAppointments.map((appointment) => (
            <div
              key={appointment.id}
              className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
              onClick={() => navigate('/appointments')}
            >
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium text-sm">
                  {appointment.customer?.firstName?.charAt(0)}{appointment.customer?.lastName?.charAt(0)}
                </div>
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {appointment.customer?.firstName} {appointment.customer?.lastName}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {appointment.service?.title}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900 dark:text-white">
                  ${appointment.service?.price || 0}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {formatLocalTime(appointment.expectedAppointmentStartTime)}
                </p>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {recentAppointments.length}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">{t('recentAppointments.appointmentsLabel', 'Appointments')}</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              ${totalRevenue.toLocaleString()}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">{t('recentAppointments.totalRevenue', 'Total Revenue')}</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              ${recentAppointments.length > 0 ? Math.round(totalRevenue / recentAppointments.length) : 0}
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">{t('recentAppointments.avgPerAppointment', 'Avg per appointment')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecentAppointmentsSimple;
