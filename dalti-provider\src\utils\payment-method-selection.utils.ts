/**
 * Payment Method Selection Utilities
 * Handles logic for determining available and recommended payment methods
 */

import { PaymentMethod, PaymentProcessor, PaymentMethodType } from '../types';
import { UserLocationInfo } from '../hooks/useUserLocation';

/**
 * Payment method selection criteria
 */
export interface PaymentMethodCriteria {
  userLocation: UserLocationInfo;
  subscriptionStatus?: 'active' | 'inactive' | 'trial' | 'past_due' | 'cancelled';
  preferredCurrency?: string;
  previousPaymentMethod?: PaymentProcessor;
  businessType?: string;
  accountAge?: number; // in days
  isFirstTimeUser?: boolean;
  hasFailedPayments?: boolean;
}

/**
 * Payment method recommendation result
 */
export interface PaymentMethodRecommendation {
  primary: PaymentMethod | null;
  alternatives: PaymentMethod[];
  reasoning: string;
  confidence: 'high' | 'medium' | 'low';
}

/**
 * Regional payment method configuration
 */
const REGIONAL_PAYMENT_CONFIG = {
  DZ: { // Algeria
    preferredProcessors: ['chargily'],
    preferredMethods: ['edahabia', 'cib'],
    currency: 'DZD',
    fallbackProcessors: ['lemonsqueezy'],
  },
  US: { // United States
    preferredProcessors: ['lemonsqueezy'],
    preferredMethods: ['card', 'paypal'],
    currency: 'USD',
    fallbackProcessors: [],
  },
  CA: { // Canada
    preferredProcessors: ['lemonsqueezy'],
    preferredMethods: ['card', 'paypal'],
    currency: 'USD',
    fallbackProcessors: [],
  },
  FR: { // France
    preferredProcessors: ['lemonsqueezy'],
    preferredMethods: ['card'],
    currency: 'USD',
    fallbackProcessors: [],
  },
  // Add more regions as needed
  DEFAULT: {
    preferredProcessors: ['lemonsqueezy'],
    preferredMethods: ['card'],
    currency: 'USD',
    fallbackProcessors: [],
  },
} as const;

/**
 * Get available payment methods for a specific location
 */
export function getAvailablePaymentMethodsForLocation(
  userLocation: string,
  allMethods: PaymentMethod[]
): PaymentMethod[] {
  return allMethods.filter(method => {
    // Show Chargily for Algerian users
    if (method.id === 'chargily') {
      return userLocation === 'DZ' || userLocation.toLowerCase().includes('algeria');
    }

    // Show LemonSqueezy for all users as fallback
    if (method.id === 'lemonsqueezy') {
      return true;
    }

    // Check regional availability
    if (method.region && method.region.length > 0) {
      return method.region.includes(userLocation) ||
             method.region.some(region => region.toLowerCase().includes(userLocation.toLowerCase()));
    }

    return method.isAvailable;
  });
}

/**
 * Enhanced payment method availability with subscription and business logic
 */
export function getAvailablePaymentMethodsWithCriteria(
  criteria: PaymentMethodCriteria,
  allMethods: PaymentMethod[]
): PaymentMethod[] {
  return allMethods.filter(method => {
    // Basic location filtering
    if (!isMethodAvailableForLocation(method, criteria.userLocation.countryCode)) {
      return false;
    }

    // Subscription status restrictions
    if (!isMethodAvailableForSubscriptionStatus(method, criteria.subscriptionStatus)) {
      return false;
    }

    // Business type restrictions
    if (!isMethodAvailableForBusinessType(method, criteria.businessType)) {
      return false;
    }

    // Account age restrictions
    if (!isMethodAvailableForAccountAge(method, criteria.accountAge, criteria.isFirstTimeUser)) {
      return false;
    }

    // Failed payments restrictions
    if (!isMethodAvailableWithFailedPayments(method, criteria.hasFailedPayments)) {
      return false;
    }

    return method.isAvailable;
  });
}

/**
 * Check if payment method is available for location
 */
function isMethodAvailableForLocation(method: PaymentMethod, countryCode: string): boolean {
  // Chargily is only for Algeria
  if (method.id === 'chargily') {
    return countryCode === 'DZ';
  }

  // LemonSqueezy is available globally
  if (method.id === 'lemonsqueezy') {
    return true;
  }

  // Check regional restrictions
  if (method.region && method.region.length > 0) {
    return method.region.includes(countryCode);
  }

  return true;
}

/**
 * Check if payment method is available for subscription status
 */
function isMethodAvailableForSubscriptionStatus(
  method: PaymentMethod,
  subscriptionStatus?: string
): boolean {
  // No restrictions for most cases
  if (!subscriptionStatus) return true;

  // Restrict certain methods for users with payment issues
  if (subscriptionStatus === 'past_due' || subscriptionStatus === 'cancelled') {
    // For users with payment issues, prefer local payment methods
    if (method.id === 'chargily') {
      return true; // Local payment methods are preferred for recovery
    }
  }

  return true;
}

/**
 * Check if payment method is available for business type
 */
function isMethodAvailableForBusinessType(
  method: PaymentMethod,
  businessType?: string
): boolean {
  // No restrictions currently, but could be extended
  // Example: certain payment methods for specific business types
  return true;
}

/**
 * Check if payment method is available based on account age
 */
function isMethodAvailableForAccountAge(
  method: PaymentMethod,
  accountAge?: number,
  isFirstTimeUser?: boolean
): boolean {
  // No restrictions currently
  // Could implement restrictions for new accounts if needed
  return true;
}

/**
 * Check if payment method is available for users with failed payments
 */
function isMethodAvailableWithFailedPayments(
  method: PaymentMethod,
  hasFailedPayments?: boolean
): boolean {
  if (!hasFailedPayments) return true;

  // For users with failed payments, prefer local payment methods
  // as they might have issues with international processors
  if (method.id === 'chargily') {
    return true; // Local payment methods are more reliable for recovery
  }

  return true; // Still allow other methods but with lower priority
}

/**
 * Get recommended payment method based on comprehensive criteria
 */
export function getRecommendedPaymentMethod(
  criteria: PaymentMethodCriteria,
  availableMethods: PaymentMethod[]
): PaymentMethodRecommendation {
  const { userLocation } = criteria;
  const countryCode = userLocation.countryCode || 'DEFAULT';
  const config = REGIONAL_PAYMENT_CONFIG[countryCode as keyof typeof REGIONAL_PAYMENT_CONFIG] ||
                 REGIONAL_PAYMENT_CONFIG.DEFAULT;

  // Filter methods available with enhanced criteria
  const filteredMethods = getAvailablePaymentMethodsWithCriteria(criteria, availableMethods);

  if (filteredMethods.length === 0) {
    return {
      primary: null,
      alternatives: [],
      reasoning: 'No payment methods available for your current situation',
      confidence: 'low',
    };
  }

  // Sort methods by preference and criteria
  const sortedMethods = sortPaymentMethodsByPreferenceWithCriteria(filteredMethods, criteria);

  const primaryMethod = sortedMethods[0];
  const alternatives = sortedMethods.slice(1);

  return {
    primary: primaryMethod,
    alternatives,
    reasoning: getEnhancedRecommendationReasoning(primaryMethod, criteria),
    confidence: getEnhancedRecommendationConfidence(primaryMethod, criteria),
  };
}

/**
 * Get recommendation reasoning text
 */
function getRecommendationReasoning(method: PaymentMethod, location: UserLocationInfo): string {
  if (method.id === 'chargily' && location.isAlgeria) {
    return 'Recommended for Algerian users - supports local payment methods (EDAHABIA, CIB) and DZD currency';
  }
  
  if (method.id === 'lemonsqueezy') {
    return 'Widely accepted international payment processor with card and PayPal support';
  }
  
  return `${method.displayName} is available in your region`;
}

/**
 * Get recommendation confidence level
 */
function getRecommendationConfidence(method: PaymentMethod, location: UserLocationInfo): 'high' | 'medium' | 'low' {
  // High confidence for regional matches
  if (method.id === 'chargily' && location.isAlgeria && location.confidence === 'high') {
    return 'high';
  }

  // Medium confidence for general availability
  if (method.isRecommended) {
    return 'medium';
  }

  return 'low';
}

/**
 * Enhanced sorting with comprehensive criteria
 */
function sortPaymentMethodsByPreferenceWithCriteria(
  methods: PaymentMethod[],
  criteria: PaymentMethodCriteria
): PaymentMethod[] {
  return [...methods].sort((a, b) => {
    // Priority 1: Previous payment method (user familiarity)
    if (criteria.previousPaymentMethod) {
      const aPrevious = a.id === criteria.previousPaymentMethod;
      const bPrevious = b.id === criteria.previousPaymentMethod;
      if (aPrevious && !bPrevious) return -1;
      if (!aPrevious && bPrevious) return 1;
    }

    // Priority 2: Regional preference
    const countryCode = criteria.userLocation.countryCode || 'DEFAULT';
    const config = REGIONAL_PAYMENT_CONFIG[countryCode as keyof typeof REGIONAL_PAYMENT_CONFIG] ||
                   REGIONAL_PAYMENT_CONFIG.DEFAULT;

    const aPreferred = config.preferredProcessors.includes(a.id as any);
    const bPreferred = config.preferredProcessors.includes(b.id as any);
    if (aPreferred && !bPreferred) return -1;
    if (!aPreferred && bPreferred) return 1;

    // Priority 3: Failed payments consideration
    if (criteria.hasFailedPayments) {
      const aLocal = a.id === 'chargily';
      const bLocal = b.id === 'chargily';
      if (aLocal && !bLocal) return -1;
      if (!aLocal && bLocal) return 1;
    }

    // Priority 4: Recommended methods
    if (a.isRecommended && !b.isRecommended) return -1;
    if (!a.isRecommended && b.isRecommended) return 1;

    // Priority 5: Availability
    if (a.isAvailable && !b.isAvailable) return -1;
    if (!a.isAvailable && b.isAvailable) return 1;

    // Final: Alphabetical order
    return a.displayName.localeCompare(b.displayName);
  });
}

/**
 * Enhanced recommendation reasoning
 */
function getEnhancedRecommendationReasoning(
  method: PaymentMethod,
  criteria: PaymentMethodCriteria
): string {
  const reasons: string[] = [];

  // Location-based reasoning
  if (method.id === 'chargily' && criteria.userLocation.isAlgeria) {
    reasons.push('Optimized for Algerian users with local payment methods');
  }

  // Previous payment method
  if (criteria.previousPaymentMethod === method.id) {
    reasons.push('You have used this payment method before');
  }

  // Failed payments consideration
  if (criteria.hasFailedPayments && method.id === 'chargily') {
    reasons.push('Local payment methods may be more reliable for your account');
  }

  // First time user
  if (criteria.isFirstTimeUser) {
    if (method.id === 'chargily' && criteria.userLocation.isAlgeria) {
      reasons.push('Best choice for new Algerian users');
    } else {
      reasons.push('Recommended for new users');
    }
  }

  // Subscription status
  if (criteria.subscriptionStatus === 'past_due' && method.id === 'chargily') {
    reasons.push('Local payment methods for account recovery');
  }

  // Default reasoning
  if (reasons.length === 0) {
    reasons.push(getRecommendationReasoning(method, criteria.userLocation));
  }

  return reasons.join(' • ');
}

/**
 * Enhanced recommendation confidence
 */
function getEnhancedRecommendationConfidence(
  method: PaymentMethod,
  criteria: PaymentMethodCriteria
): 'high' | 'medium' | 'low' {
  let score = 0;

  // Location confidence
  if (method.id === 'chargily' && criteria.userLocation.isAlgeria) {
    score += criteria.userLocation.confidence === 'high' ? 3 : 2;
  }

  // Previous usage
  if (criteria.previousPaymentMethod === method.id) {
    score += 2;
  }

  // Method recommendation
  if (method.isRecommended) {
    score += 1;
  }

  // Account stability
  if (criteria.subscriptionStatus === 'active' && !criteria.hasFailedPayments) {
    score += 1;
  }

  if (score >= 4) return 'high';
  if (score >= 2) return 'medium';
  return 'low';
}

/**
 * Sort payment methods by preference for a location
 */
export function sortPaymentMethodsByPreference(
  methods: PaymentMethod[],
  userLocation: UserLocationInfo
): PaymentMethod[] {
  const countryCode = userLocation.countryCode || 'DEFAULT';
  const config = REGIONAL_PAYMENT_CONFIG[countryCode as keyof typeof REGIONAL_PAYMENT_CONFIG] || 
                 REGIONAL_PAYMENT_CONFIG.DEFAULT;

  return [...methods].sort((a, b) => {
    // Preferred processors first
    const aPreferred = config.preferredProcessors.includes(a.id as any);
    const bPreferred = config.preferredProcessors.includes(b.id as any);
    
    if (aPreferred && !bPreferred) return -1;
    if (!aPreferred && bPreferred) return 1;
    
    // Recommended methods next
    if (a.isRecommended && !b.isRecommended) return -1;
    if (!a.isRecommended && b.isRecommended) return 1;
    
    // Available methods next
    if (a.isAvailable && !b.isAvailable) return -1;
    if (!a.isAvailable && b.isAvailable) return 1;
    
    // Alphabetical order as final tiebreaker
    return a.displayName.localeCompare(b.displayName);
  });
}

/**
 * Validate payment method selection
 */
export function validatePaymentMethodSelection(
  paymentProcessor: PaymentProcessor,
  paymentMethod: PaymentMethodType,
  availableMethods: PaymentMethod[]
): { isValid: boolean; error?: string } {
  const method = availableMethods.find(m => m.id === paymentProcessor);
  
  if (!method) {
    return {
      isValid: false,
      error: `Payment processor "${paymentProcessor}" is not available`,
    };
  }
  
  if (!method.isAvailable) {
    return {
      isValid: false,
      error: `${method.displayName} is currently unavailable`,
    };
  }
  
  if (!method.supportedMethods.includes(paymentMethod)) {
    return {
      isValid: false,
      error: `Payment method "${paymentMethod}" is not supported by ${method.displayName}`,
    };
  }
  
  return { isValid: true };
}

/**
 * Get payment method features for display
 */
export function getPaymentMethodFeatures(method: PaymentMethod): {
  supportedMethods: string[];
  currency: string;
  features: string[];
  benefits: string[];
} {
  const benefits: string[] = [];
  
  if (method.id === 'chargily') {
    benefits.push('Local Algerian support');
    benefits.push('DZD currency');
    benefits.push('No international fees');
  }
  
  if (method.id === 'lemonsqueezy') {
    benefits.push('International support');
    benefits.push('Multiple currencies');
    benefits.push('Instant processing');
  }
  
  return {
    supportedMethods: method.supportedMethods,
    currency: method.currency,
    features: method.features,
    benefits,
  };
}

/**
 * Check if payment method is optimal for user
 */
export function isOptimalPaymentMethod(
  method: PaymentMethod,
  userLocation: UserLocationInfo
): boolean {
  // Chargily is optimal for Algerian users
  if (method.id === 'chargily' && userLocation.isAlgeria) {
    return true;
  }
  
  // LemonSqueezy is optimal for international users
  if (method.id === 'lemonsqueezy' && !userLocation.isAlgeria) {
    return true;
  }
  
  return false;
}

/**
 * Get payment method selection analytics
 */
export function getPaymentMethodAnalytics(
  selectedMethod: PaymentMethod,
  userLocation: UserLocationInfo,
  availableMethods: PaymentMethod[]
): {
  isOptimal: boolean;
  alternativeCount: number;
  regionMatch: boolean;
  currencyMatch: boolean;
} {
  const countryCode = userLocation.countryCode || 'DEFAULT';
  const config = REGIONAL_PAYMENT_CONFIG[countryCode as keyof typeof REGIONAL_PAYMENT_CONFIG] || 
                 REGIONAL_PAYMENT_CONFIG.DEFAULT;

  return {
    isOptimal: isOptimalPaymentMethod(selectedMethod, userLocation),
    alternativeCount: availableMethods.length - 1,
    regionMatch: selectedMethod.region?.includes(userLocation.countryCode) || false,
    currencyMatch: selectedMethod.currency === config.currency,
  };
}
