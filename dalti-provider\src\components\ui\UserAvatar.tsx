import React from 'react';
import { useHasProfilePicture } from '../../hooks/useUser';
import { UserCircleIcon } from '../../icons';

export interface UserAvatarProps {
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  className?: string;
  showOnlineStatus?: boolean;
  fallbackSrc?: string;
  alt?: string;
}

const sizeClasses = {
  small: 'w-8 h-8',
  medium: 'w-11 h-11', 
  large: 'w-16 h-16',
  xlarge: 'w-24 h-24',
};

const iconSizeClasses = {
  small: 'w-5 h-5',
  medium: 'w-8 h-8',
  large: 'w-10 h-10', 
  xlarge: 'w-16 h-16',
};

export default function UserAvatar({
  size = 'medium',
  className = '',
  showOnlineStatus = false,
  fallbackSrc,
  alt = 'User Profile',
}: UserAvatarProps) {
  const { hasProfilePicture, profilePictureUrl, isLoading } = useHasProfilePicture();

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    const icon = img.nextElementSibling as HTMLElement;
    
    // Hide the failed image and show the icon
    img.style.display = 'none';
    if (icon) {
      icon.classList.remove('hidden');
    }
  };

  const displayImageUrl = profilePictureUrl || fallbackSrc;

  return (
    <div className={`relative overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center ${sizeClasses[size]} ${className}`}>
      {/* Loading state */}
      {isLoading && (
        <div className="animate-pulse bg-gray-200 dark:bg-gray-700 w-full h-full rounded-full" />
      )}
      
      {/* Profile picture or fallback image */}
      {!isLoading && displayImageUrl && (
        <img
          src={displayImageUrl}
          alt={alt}
          className="w-full h-full object-cover"
          onError={handleImageError}
        />
      )}
      
      {/* Fallback icon */}
      <UserCircleIcon 
        className={`text-gray-400 ${iconSizeClasses[size]} ${
          !isLoading && displayImageUrl ? 'hidden' : ''
        }`}
      />
      
      {/* Online status indicator */}
      {showOnlineStatus && (
        <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></span>
      )}
    </div>
  );
}
