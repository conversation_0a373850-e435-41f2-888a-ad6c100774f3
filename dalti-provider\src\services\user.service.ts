import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import { UploadUrlResponse } from '../utils/s3-upload.utils';
import { LanguageCode } from '../context/LanguageContext';

/**
 * Profile picture response types
 */
export interface ProfilePictureResponse {
  hasProfilePicture: boolean;
  profilePicture?: {
    id: string;
    name: string;
    type: string;
    key: string;
    downloadUrl: string;
    createdAt: string;
  };
}

export interface ProfilePictureApiResponse {
  success: boolean;
  message?: string;
  data: ProfilePictureResponse;
}

export interface UploadUrlApiResponse {
  success: boolean;
  message: string;
  data: UploadUrlResponse;
}

export interface DeleteResponse {
  success: boolean;
  message: string;
}

/**
 * Update preferred language request and response types
 */
export interface UpdatePreferredLanguageRequest {
  preferedLanguage: 'EN' | 'AR' | 'FR'; // Backend expects uppercase
}

export interface UpdatePreferredLanguageResponse {
  success: boolean;
  message: string;
  data?: {
    preferedLanguage: 'EN' | 'AR' | 'FR';
  };
}

/**
 * User service for profile picture management operations
 */
export class UserService {
  /**
   * Generate upload URL for profile picture
   * Phase 1 of the two-phase upload workflow
   */
  static async generateProfilePictureUploadUrl(
    fileName: string,
    fileType: string
  ): Promise<UploadUrlResponse> {
    const response = await apiClient.post<UploadUrlApiResponse>(
      config.endpoints.user.profilePicture,
      {
        fileName,
        fileType,
      }
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to generate upload URL');
    }

    return response.data.data;
  }

  /**
   * Get current user's profile picture
   */
  static async getProfilePicture(): Promise<ProfilePictureResponse> {
    const response = await apiClient.get<ProfilePictureApiResponse>(
      config.endpoints.user.profilePicture
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to fetch profile picture');
    }

    return response.data.data;
  }

  /**
   * Delete user's profile picture
   */
  static async deleteProfilePicture(): Promise<void> {
    const response = await apiClient.delete<DeleteResponse>(
      config.endpoints.user.profilePicture
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete profile picture');
    }
  }

  /**
   * Check if user has a profile picture
   */
  static async hasProfilePicture(): Promise<boolean> {
    try {
      const profilePicture = await this.getProfilePicture();
      return profilePicture.hasProfilePicture;
    } catch (error) {
      // If there's an error fetching, assume no profile picture
      return false;
    }
  }

  /**
   * Get profile picture download URL
   */
  static async getProfilePictureUrl(): Promise<string | null> {
    try {
      const profilePicture = await this.getProfilePicture();
      return profilePicture.profilePicture?.downloadUrl || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Update user's preferred language
   */
  static async updatePreferredLanguage(languageCode: LanguageCode): Promise<void> {
    // Convert lowercase language code to uppercase for backend API
    const backendLanguageCode = languageCode.toUpperCase() as 'EN' | 'AR' | 'FR';

    const response = await apiClient.post<UpdatePreferredLanguageResponse>(
      config.endpoints.user.updatePreferredLanguage,
      {
        preferedLanguage: backendLanguageCode
      }
    );

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to update preferred language');
    }
  }
}
