import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import PageBreadcrumb from "../components/common/PageBreadCrumb";
import PageMeta from "../components/common/PageMeta";
import Button from "../components/ui/button/Button";
import Label from "../components/form/Label";
import Input from "../components/form/input/InputField";
import { ErrorDisplay } from "../components/error";
import { useAuth } from "../context/AuthContext";
import {
  useProviderProfile,
  useUpdateProviderProfile,
  useUploadLogo,
  useDeleteLogo,
  useHasProviderLogo
} from "../hooks/useProvider";
import {
  useProfilePictureManager
} from "../hooks/useUser";
import { useProfileCompletion } from "../hooks/useProfileCompletion";
import ProfilePageCompletionAlert from "../components/profile-completion/ProfilePageCompletionAlert";
import { useProviderCategories } from "../hooks/useAuthMutations";
import { ProviderProfileUpdateRequest } from "../types";
import TwoStepCategorySelector from "../components/form/TwoStepCategorySelector";
import UserAvatar from "../components/ui/UserAvatar";
import BusinessLogo from "../components/ui/BusinessLogo";
import {
  SubscriptionStatusWidget,
  CustomerPortalCard,
  UsageStatistics
} from "../components/subscription";
import { useSubscriptionStatus } from "../hooks/useSubscriptionGuard";
import { useManagementTranslation, useCommonTranslation, useTranslation } from "../hooks/useTranslation";

// Validation schema
const profileSchema = z.object({
  title: z.string().min(2, "Business name must be at least 2 characters"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  presentation: z.string().optional(),
  providerCategoryId: z.number().min(1, "Please select a category"),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export default function UserProfiles() {
  const { user, provider } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoUploadProgress, setLogoUploadProgress] = useState(0);
  const [profilePictureFile, setProfilePictureFile] = useState<File | null>(null);
  const [profilePictureUploadProgress, setProfilePictureUploadProgress] = useState(0);
  const [highlightedSection, setHighlightedSection] = useState<string | null>(null);
  const { t, currentLanguage } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();
  const { t: tForms } = useTranslation('forms');

  // Custom translations for profile page
  const profileTranslations = {
    ar: {
      title: "الملف الشخصي",
      businessProfile: "الملف التجاري",
      accountInformation: "معلومات الحساب",
      profilePicture: "صورة الملف الشخصي",
      businessLogo: "شعار الشركة",
      businessName: "اسم الشركة",
      phoneNumber: "رقم الهاتف",
      businessDescription: "وصف الشركة",
      email: "البريد الإلكتروني",
      accountName: "اسم الحساب",
      businessCategory: "فئة الشركة",
      businessNamePlaceholder: "أدخل اسم شركتك",
      phoneNumberPlaceholder: "أدخل رقم هاتفك",
      businessDescriptionPlaceholder: "أخبر العملاء عن شركتك...",
      editProfile: "تعديل الملف الشخصي",
      cancel: "إلغاء",
      uploadPicture: "رفع الصورة",
      uploadLogo: "رفع الشعار",
      removePicture: "إزالة الصورة",
      removeLogo: "إزالة الشعار",
      saveChanges: "حفظ التغييرات",
      uploading: "جاري الرفع...",
      removing: "جاري الإزالة...",
      saving: "جاري الحفظ...",
      noCategorySelected: "لم يتم اختيار فئة"
    },
    en: {
      title: "Profile",
      businessProfile: "Business Profile",
      accountInformation: "Account Information",
      profilePicture: "Profile Picture",
      businessLogo: "Business Logo",
      businessName: "Business Name",
      phoneNumber: "Phone Number",
      businessDescription: "Business Description",
      email: "Email",
      accountName: "Account Name",
      businessCategory: "Business Category",
      businessNamePlaceholder: "Enter your business name",
      phoneNumberPlaceholder: "Enter your phone number",
      businessDescriptionPlaceholder: "Tell customers about your business...",
      editProfile: "Edit Profile",
      cancel: "Cancel",
      uploadPicture: "Upload Picture",
      uploadLogo: "Upload Logo",
      removePicture: "Remove Picture",
      removeLogo: "Remove Logo",
      saveChanges: "Save Changes",
      uploading: "Uploading...",
      removing: "Removing...",
      saving: "Saving...",
      noCategorySelected: "No category selected"
    },
    fr: {
      title: "Profil",
      businessProfile: "Profil d'entreprise",
      accountInformation: "Informations du compte",
      profilePicture: "Photo de profil",
      businessLogo: "Logo de l'entreprise",
      businessName: "Nom de l'entreprise",
      phoneNumber: "Numéro de téléphone",
      businessDescription: "Description de l'entreprise",
      email: "Email",
      accountName: "Nom du compte",
      businessCategory: "Catégorie d'entreprise",
      businessNamePlaceholder: "Entrez le nom de votre entreprise",
      phoneNumberPlaceholder: "Entrez votre numéro de téléphone",
      businessDescriptionPlaceholder: "Parlez de votre entreprise aux clients...",
      editProfile: "Modifier le profil",
      cancel: "Annuler",
      uploadPicture: "Télécharger une photo",
      uploadLogo: "Télécharger un logo",
      removePicture: "Supprimer la photo",
      removeLogo: "Supprimer le logo",
      saveChanges: "Enregistrer les modifications",
      uploading: "Téléchargement...",
      removing: "Suppression...",
      saving: "Enregistrement...",
      noCategorySelected: "Aucune catégorie sélectionnée"
    }
  };

  const currentLang = currentLanguage as keyof typeof profileTranslations;
  const pt = (key: keyof typeof profileTranslations.ar) =>
    profileTranslations[currentLang]?.[key] || profileTranslations.en[key] || key;

  const { data: profileData, isLoading: profileLoading } = useProviderProfile();
  const { completion: completionData, isLoading: completionLoading } = useProfileCompletion();
  const { data: categories } = useProviderCategories();
  const updateProfileMutation = useUpdateProviderProfile();
  const uploadLogoMutation = useUploadLogo();
  const deleteLogoMutation = useDeleteLogo();
  const { status: subscriptionStatus } = useSubscriptionStatus();

  // Profile picture management
  const {
    profilePictureUrl,
    hasProfilePicture,
    isLoading: profilePictureLoading,
    isUploading: profilePictureUploading,
    isDeleting: profilePictureDeleting,
    uploadProfilePicture,
    deleteProfilePicture,
  } = useProfilePictureManager();

  // Business logo management
  const {
    hasLogo,
    logoUrl,
    isLoading: logoLoading,
  } = useHasProviderLogo();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      title: profileData?.title || '',
      phone: profileData?.phone || '',
      presentation: profileData?.presentation || '',
      providerCategoryId: profileData?.category?.id || 0,
    },
  });

  // Update form when profile data changes
  React.useEffect(() => {
    if (profileData) {
      reset({
        title: profileData.title || '',
        phone: profileData.phone || '',
        presentation: profileData.presentation || '',
        providerCategoryId: profileData.category?.id || 0,
      });
    }
  }, [profileData, reset]);

  const onSubmit = async (data: ProfileFormData) => {
    try {
      await updateProfileMutation.mutateAsync(data);
      setIsEditing(false);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleLogoSelect = (file: File) => {
    setLogoFile(file);
  };

  const handleLogoUpload = async () => {
    if (logoFile) {
      try {
        await uploadLogoMutation.mutateAsync({
          file: logoFile,
          onProgress: setLogoUploadProgress,
        });
        setLogoFile(null);
        setLogoUploadProgress(0);
      } catch (error) {
        setLogoUploadProgress(0);
        // Error handled by mutation
      }
    }
  };

  const handleLogoRemove = async () => {
    try {
      await deleteLogoMutation.mutateAsync();
    } catch (error) {
      // Error handled by mutation
    }
  };

  // Profile picture handlers
  const handleProfilePictureSelect = (file: File) => {
    setProfilePictureFile(file);
  };

  const handleProfilePictureUpload = async () => {
    if (profilePictureFile) {
      try {
        await uploadProfilePicture({
          file: profilePictureFile,
          onProgress: setProfilePictureUploadProgress,
        });
        setProfilePictureFile(null);
        setProfilePictureUploadProgress(0);
      } catch (error) {
        setProfilePictureUploadProgress(0);
        // Error handled by mutation
      }
    }
  };

  const handleProfilePictureRemove = async () => {
    try {
      await deleteProfilePicture();
      setProfilePictureFile(null);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const isLoading = profileLoading || updateProfileMutation.isPending;

  return (
    <>
      <PageMeta
        title="Provider Profile | Provider Dashboard"
        description="Manage your provider profile and business information"
      />
      <PageBreadcrumb pageTitle={pt('title')} />

      <div className="space-y-6">
        {/* Profile Completion Status */}
        <ProfilePageCompletionAlert
          className="mb-6"
          onSectionFocus={(section) => {
            setHighlightedSection(section);
            // Clear highlight after 3 seconds
            setTimeout(() => setHighlightedSection(null), 3000);
          }}
        />

        {/* Profile Information */}
        <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90">
              {pt('businessProfile')}
            </h3>
            <Button
              onClick={() => setIsEditing(!isEditing)}
              variant="outline"
              size="sm"
              disabled={isLoading}
            >
              {isEditing ? pt('cancel') : pt('editProfile')}
            </Button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Profile Picture Section */}
            <div className="flex items-start space-x-6">
              <div className="flex-shrink-0">
                <div className="h-24 w-24 rounded-full border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center overflow-hidden">
                  <UserAvatar size="xlarge" className="w-full h-full border-0" />
                </div>
              </div>

              <div
                className={`flex-1 transition-all duration-300 ${
                  highlightedSection === 'profilePicture'
                    ? 'ring-2 ring-blue-500 ring-opacity-50 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 -m-3'
                    : ''
                }`}
                id="profile-picture-section"
              >
                <Label>{pt('profilePicture')}</Label>
                <div className="mt-2 space-y-3">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleProfilePictureSelect(file);
                    }}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-brand-50 file:text-brand-700 hover:file:bg-brand-100 dark:file:bg-brand-900/20 dark:file:text-brand-400"
                    disabled={isLoading || profilePictureLoading}
                  />

                  {profilePictureFile && (
                    <div className="flex space-x-2">
                      <Button
                        type="button"
                        onClick={handleProfilePictureUpload}
                        size="sm"
                        disabled={profilePictureUploading}
                      >
                        {profilePictureUploading ? tForms('buttons.uploading') : tForms('buttons.uploadPicture')}
                      </Button>
                      <Button
                        type="button"
                        onClick={() => {
                          setProfilePictureFile(null);
                        }}
                        variant="outline"
                        size="sm"
                      >
                        {tForms('buttons.cancel')}
                      </Button>
                    </div>
                  )}

                  {hasProfilePicture && !profilePictureFile && (
                    <Button
                      type="button"
                      onClick={handleProfilePictureRemove}
                      variant="outline"
                      size="sm"
                      disabled={profilePictureDeleting}
                    >
                      {profilePictureDeleting ? tForms('buttons.removing') : tForms('buttons.removePicture')}
                    </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Business Logo Section */}
            <div className="flex items-start space-x-6">
              <div className="flex-shrink-0">
                <div className="h-24 w-24 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center overflow-hidden">
                  <BusinessLogo size="xlarge" className="w-full h-full border-0" shape="square" />
                </div>
              </div>

              <div
                className={`flex-1 transition-all duration-300 ${
                  highlightedSection === 'businessLogo'
                    ? 'ring-2 ring-blue-500 ring-opacity-50 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 -m-3'
                    : ''
                }`}
                id="logo-section"
              >
                <Label>{pt('businessLogo')}</Label>
                <div className="mt-2 space-y-3">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleLogoSelect(file);
                    }}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-brand-50 file:text-brand-700 hover:file:bg-brand-100 dark:file:bg-brand-900/20 dark:file:text-brand-400"
                    disabled={isLoading}
                  />

                  {logoFile && (
                    <div className="flex space-x-2">
                      <Button
                        type="button"
                        onClick={handleLogoUpload}
                        size="sm"
                        disabled={uploadLogoMutation.isPending}
                      >
                        {uploadLogoMutation.isPending ? tForms('buttons.uploading') : tForms('buttons.uploadLogo')}
                      </Button>
                      <Button
                        type="button"
                        onClick={() => {
                          setLogoFile(null);
                        }}
                        variant="outline"
                        size="sm"
                      >
                        {tForms('buttons.cancel')}
                      </Button>
                    </div>
                  )}

                  {hasLogo && !logoFile && (
                    <Button
                      type="button"
                      onClick={handleLogoRemove}
                      variant="outline"
                      size="sm"
                      disabled={deleteLogoMutation.isPending}
                    >
                      {deleteLogoMutation.isPending ? tForms('buttons.removing') : tForms('buttons.removeLogo')}
                    </Button>
                  )}
                </div>
              </div>
            </div>



            {/* Basic Information */}
            <div
              className={`grid grid-cols-1 gap-6 sm:grid-cols-2 transition-all duration-300 ${
                highlightedSection === 'providerInfo'
                  ? 'ring-2 ring-blue-500 ring-opacity-50 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 -m-3'
                  : ''
              }`}
              id="basic-info-section"
            >
              <div>
                <Label>
                  {pt('businessName')} <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('title')}
                  placeholder={pt('businessNamePlaceholder')}
                  disabled={!isEditing || isLoading}
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.title.message}
                  </p>
                )}
              </div>

              <div>
                <Label>
                  {pt('phoneNumber')} <span className="text-red-500">*</span>
                </Label>
                <Input
                  {...register('phone')}
                  placeholder={pt('phoneNumberPlaceholder')}
                  disabled={!isEditing || isLoading}
                />
                {errors.phone && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {errors.phone.message}
                  </p>
                )}
              </div>
            </div>
            <div>
              <Label>{pt('businessDescription')}</Label>
              <textarea
                {...register('presentation')}
                rows={4}
                placeholder={pt('businessDescriptionPlaceholder')}
                disabled={!isEditing || isLoading}
                className="w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800 disabled:opacity-50 disabled:cursor-not-allowed"
              />
            </div>

            {/* Account Information (Read-only) */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-md font-medium text-gray-800 dark:text-white/90 mb-4">
                {pt('accountInformation')}
              </h4>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <Label>{pt('email')}</Label>
                  <Input
                    value={user?.email || ''}
                    disabled
                    className="bg-gray-50 dark:bg-gray-800"
                  />
                </div>
                <div>
                  <Label>{pt('accountName')}</Label>
                  <Input
                    value={`${user?.firstName || ''} ${user?.lastName || ''}`.trim()}
                    disabled
                    className="bg-gray-50 dark:bg-gray-800"
                  />
                </div>
                <div>
                  <Label>{pt('businessCategory')}</Label>
                  <Input
                    value={profileData?.category?.title || pt('noCategorySelected')}
                    disabled
                    className="bg-gray-50 dark:bg-gray-800"
                  />
                </div>
              </div>
            </div>

            {updateProfileMutation.error && (
              <ErrorDisplay
                error={updateProfileMutation.error}
                variant="banner"
                size="sm"
              />
            )}

            {isEditing && (
              <div className="flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                  disabled={isLoading}
                >
                  {pt('cancel')}
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? pt('saving') : pt('saveChanges')}
                </Button>
              </div>
            )}
          </form>
        </div>
      </div>
    </>
  );
}
