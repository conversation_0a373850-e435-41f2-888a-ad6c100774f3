import { useState, useRef, useMemo, useCallback, useEffect } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { EventInput, DateSelectArg, EventClickArg } from "@fullcalendar/core";
import { Modal } from "../components/ui/modal";
import { useModal } from "../hooks/useModal";
import PageMeta from "../components/common/PageMeta";
import { useAppointments } from "../hooks/useAppointments";
import { Appointment } from "../types/appointment";
import AppointmentForm from "../components/appointments/AppointmentForm";
import AppointmentDetails from "../components/appointments/AppointmentDetails";
import { ErrorDisplay } from "../components/error";
import CalendarSidebar from "../components/calendar/CalendarSidebar";
import ServiceForm from "../components/services/ServiceForm";
import CalendarEventCard from "../components/calendar/CalendarEventCard";
import { groupAppointmentsByTimeSlots } from "../utils/calendarUtils";
import { useCalendarTranslation, useCommonTranslation } from "../hooks/useTranslation";
import { useLanguage } from "../context/LanguageContext";
import { useResponsive } from "../hooks/useMediaQuery";

interface CalendarEvent extends EventInput {
  extendedProps: {
    appointmentId?: number;
    appointment?: Appointment;
    status: string;
    customerName?: string;
    serviceName?: string;
    groupIndex?: number;
    appointmentIndex?: number;
    isLastInGroup?: boolean;
    hiddenCount?: number;
    allGroupAppointments?: Appointment[];
  };
}

const Calendar: React.FC = () => {
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'create' | 'view' | null>(null);
  const calendarRef = useRef<FullCalendar>(null);
  const { isOpen, openModal, closeModal } = useModal();
  const { t } = useCalendarTranslation();
  const { t: tCommon } = useCommonTranslation();
  const { currentLanguage, isRTL } = useLanguage();
  const { isMobile, isTablet } = useResponsive();

  // Get FullCalendar locale configuration
  const getCalendarLocale = () => {
    switch (currentLanguage) {
      case 'fr':
        return 'fr';
      case 'ar':
        return 'ar';
      default:
        return 'en';
    }
  };

  // Get localized button text for FullCalendar
  const getButtonText = () => {
    return {
      today: t('today', 'Today'),
      month: t('views.month'),
      week: t('views.week'),
      day: t('views.day'),
      list: t('list', 'List')
    };
  };

  // Service modal state
  const { isOpen: isServiceModalOpen, openModal: openServiceModal, closeModal: closeServiceModal } = useModal();

  // Sidebar state - auto-collapse on mobile
  const [sidebarCollapsed, setSidebarCollapsed] = useState(() => {
    const saved = localStorage.getItem('calendar-sidebar-collapsed');
    // Auto-collapse on mobile devices
    if (isMobile) return true;
    return saved ? JSON.parse(saved) : false;
  });

  // Auto-collapse sidebar on mobile
  useEffect(() => {
    if (isMobile && !sidebarCollapsed) {
      setSidebarCollapsed(true);
      localStorage.setItem('calendar-sidebar-collapsed', JSON.stringify(true));
    }
  }, [isMobile, sidebarCollapsed]);

  // Force day view on mobile
  useEffect(() => {
    if (calendarRef.current && isMobile) {
      const calendarApi = calendarRef.current.getApi();
      const currentView = calendarApi.view.type;

      // Force day view on mobile
      if (currentView !== 'timeGridDay') {
        calendarApi.changeView('timeGridDay');
      }
    }
  }, [isMobile]);

  // Calendar configuration state
  const [calendarConfig, setCalendarConfig] = useState(() => {
    try {
      const saved = localStorage.getItem('calendar-config');
      if (saved) {
        const parsed = JSON.parse(saved);
        // Validate the parsed config
        if (parsed &&
            typeof parsed.startTime === 'string' &&
            typeof parsed.endTime === 'string' &&
            typeof parsed.timeSlotInterval === 'number' &&
            parsed.startTime.match(/^\d{2}:\d{2}$/) &&
            parsed.endTime.match(/^\d{2}:\d{2}$/) &&
            parsed.timeSlotInterval > 0) {
          return parsed;
        }
      }
    } catch (error) {
      console.warn('Failed to parse calendar config from localStorage:', error);
    }

    // Return default config if parsing fails or data is invalid
    return {
      startTime: '08:00',
      endTime: '18:00',
      timeSlotInterval: 30,
    };
  });

  // Filter state
  const [selectedServices, setSelectedServices] = useState<number[]>(() => {
    const saved = localStorage.getItem('calendar-selected-services');
    return saved ? JSON.parse(saved) : [];
  });
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(() => {
    const saved = localStorage.getItem('calendar-selected-statuses');
    return saved ? JSON.parse(saved) : ['pending', 'confirmed', 'completed', 'cancelled'];
  });

  // Fetch appointments data
  const { data: appointments, isLoading, error } = useAppointments();

  // Debug calendar config changes
  useEffect(() => {
    console.log('Calendar config updated:', calendarConfig);
    console.log('FullCalendar props will be:', {
      slotMinTime: calendarConfig.startTime || '08:00',
      slotMaxTime: calendarConfig.endTime || '18:00',
      slotDuration: `00:${calendarConfig.timeSlotInterval || 30}:00`
    });
  }, [calendarConfig]);

  // Filter appointments based on selected services and statuses
  const filteredAppointments = useMemo(() => {
    if (!appointments || !Array.isArray(appointments)) return [];

    return appointments.filter((appointment: Appointment) => {
      // Filter by services
      const serviceMatch = selectedServices.length === 0 ||
        (appointment.service?.id && selectedServices.includes(appointment.service.id));

      // Filter by status
      const statusMatch = selectedStatuses.includes(appointment.status);

      return serviceMatch && statusMatch;
    });
  }, [appointments, selectedServices, selectedStatuses]);

  // Get color based on appointment status
  const getStatusColor = useCallback((status: string): string => {
    switch (status) {
      case 'pending':
        return '#F59E0B'; // yellow-500
      case 'confirmed':
        return '#10B981'; // green-500
      case 'completed':
        return '#3B82F6'; // blue-500
      case 'cancelled':
      case 'canceled':
        return '#EF4444'; // red-500
      case 'InProgress':
        return '#8B5CF6'; // purple
      case 'noshow':
      case 'no-show':
        return '#6B7280'; // gray
      default:
        return '#8B5CF6'; // purple
    }
  }, []);

  // Transform appointments to calendar events with 3-event limit per time slot
  const events: CalendarEvent[] = useMemo(() => {
    if (!filteredAppointments || !Array.isArray(filteredAppointments)) return [];

    // Group appointments by time slots
    const timeSlotGroups = groupAppointmentsByTimeSlots(filteredAppointments, calendarConfig.timeSlotInterval || 30);

    // Debug logging (can be removed in production)
    console.log('Time slot groups created:', timeSlotGroups.length);
    timeSlotGroups.forEach((group, i) => {
      if (group.appointments.length > 1) {
        console.log(`Group ${i}: ${group.appointments.length} appointments in slot ${new Date(group.startTime).toLocaleTimeString()}-${new Date(group.endTime).toLocaleTimeString()}`);
      }
    });

    const allEvents: CalendarEvent[] = [];

    timeSlotGroups.forEach((group, groupIndex) => {
      const appointments = group.appointments;

      // Create individual events for up to 3 appointments
      const visibleAppointments = appointments.slice(0, 3);
      const hiddenCount = Math.max(0, appointments.length - 3);

      visibleAppointments.forEach((appointment: any, appointmentIndex) => {
        const startDateTime = new Date(appointment.expectedAppointmentStartTime);
        const endDateTime = new Date(appointment.expectedAppointmentEndTime);

        allEvents.push({
          id: `${appointment.id}-${groupIndex}-${appointmentIndex}`,
          title: `${appointment.service?.title || 'Service'} - ${appointment.customer?.firstName} ${appointment.customer?.lastName}`,
          start: startDateTime.toISOString(),
          end: endDateTime.toISOString(),
          backgroundColor: getStatusColor(appointment.status),
          borderColor: getStatusColor(appointment.status),
          textColor: '#ffffff',
          extendedProps: {
            appointmentId: appointment.id,
            appointment: appointment,
            status: appointment.status,
            customerName: `${appointment.customer?.firstName} ${appointment.customer?.lastName}`,
            serviceName: appointment.service?.title,
            groupIndex,
            appointmentIndex,
            isLastInGroup: appointmentIndex === visibleAppointments.length - 1,
            hiddenCount: appointmentIndex === visibleAppointments.length - 1 ? hiddenCount : 0,
            allGroupAppointments: hiddenCount > 0 ? appointments : null,
          },
        } as CalendarEvent);
      });
    });

    return allEvents;
  }, [filteredAppointments, getStatusColor]);

  const handleDateSelect = (selectInfo: DateSelectArg) => {
    // Pass the selectInfo to get both date and time information
    setSelectedDate(selectInfo.startStr);
    setSelectedAppointment(null);
    setViewMode('create');
    openModal();
  };

  const handleEventClick = (clickInfo: EventClickArg) => {
    const { appointment } = clickInfo.event.extendedProps;

    if (appointment) {
      setSelectedAppointment(appointment as any);
      setSelectedDate(null);
      setViewMode('view');
      openModal();
    }
  };

  // Handle event mouse enter for tooltip
  const handleEventMouseEnter = useCallback((mouseEnterInfo: any) => {
    const { appointment } = mouseEnterInfo.event.extendedProps;

    if (appointment) {
      const eventEl = mouseEnterInfo.el;
      if (eventEl) {
        showEventTooltip(eventEl, appointment);
      }
    }
  }, []);

  // Handle event mouse leave for tooltip
  const handleEventMouseLeave = useCallback((_mouseLeaveInfo: any) => {
    // Hide tooltip
    hideEventTooltip();
  }, []);

  // Tooltip state and functions
  const [tooltipState, setTooltipState] = useState<{
    visible: boolean;
    appointment: Appointment | null;
    appointments?: Appointment[]; // For group tooltips
    position: { x: number; y: number };
  }>({
    visible: false,
    appointment: null,
    position: { x: 0, y: 0 }
  });

  // Track if any modal is open to disable tooltips
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Modal state for "+N" indicator
  const [modalState, setModalState] = useState<{
    visible: boolean;
    appointments: Appointment[];
    position: { x: number; y: number };
  }>({
    visible: false,
    appointments: [],
    position: { x: 0, y: 0 }
  });



  const showEventTooltip = (eventEl: HTMLElement, appointment: Appointment) => {
    // Don't show tooltip if a modal is open
    if (isModalOpen) return;

    const rect = eventEl.getBoundingClientRect();
    setTooltipState({
      visible: true,
      appointment,
      position: {
        x: rect.left + rect.width / 2,
        y: rect.top - 10  // Position above the event with 10px gap
      }
    });
  };



  const hideEventTooltip = () => {
    setTooltipState({
      visible: false,
      appointment: null,
      position: { x: 0, y: 0 }
    });
  };

  const handleCreateAppointment = () => {
    setSelectedAppointment(null);
    setSelectedDate(null);
    setViewMode('create');
    openModal();
  };

  const handleCloseModal = () => {
    setSelectedAppointment(null);
    setSelectedDate(null);
    setViewMode(null);
    closeModal();
  };

  const handleAppointmentSuccess = () => {
    handleCloseModal();
  };

  // Sidebar handlers
  const handleSidebarToggle = () => {
    const newCollapsed = !sidebarCollapsed;
    setSidebarCollapsed(newCollapsed);
    localStorage.setItem('calendar-sidebar-collapsed', JSON.stringify(newCollapsed));
  };

  const handleConfigChange = (config: typeof calendarConfig) => {
    // Validate config values before setting
    const validatedConfig = {
      startTime: config.startTime || '08:00',
      endTime: config.endTime || '18:00',
      timeSlotInterval: config.timeSlotInterval || 30,
    };

    // Ensure end time is after start time
    if (validatedConfig.startTime >= validatedConfig.endTime) {
      console.warn('End time must be after start time. Adjusting end time.');
      const startHour = parseInt(validatedConfig.startTime.split(':')[0]);
      validatedConfig.endTime = `${String(startHour + 1).padStart(2, '0')}:00`;
    }

    console.log('Calendar config changing:', validatedConfig);
    setCalendarConfig(validatedConfig);
    localStorage.setItem('calendar-config', JSON.stringify(validatedConfig));

    // Update FullCalendar options dynamically
    setTimeout(() => {
      if (calendarRef.current) {
        const calendarApi = calendarRef.current.getApi();

        console.log('Current calendar view:', calendarApi.view.type);
        console.log('Before update - Current options:', {
          slotMinTime: calendarApi.getOption('slotMinTime'),
          slotMaxTime: calendarApi.getOption('slotMaxTime'),
          slotDuration: calendarApi.getOption('slotDuration')
        });

        // Update the slot times using setOption
        calendarApi.setOption('slotMinTime', validatedConfig.startTime);
        calendarApi.setOption('slotMaxTime', validatedConfig.endTime);
        calendarApi.setOption('slotDuration', `00:${validatedConfig.timeSlotInterval < 10 ? '0' + validatedConfig.timeSlotInterval : validatedConfig.timeSlotInterval}:00`);

        console.log('After update - New options:', {
          slotMinTime: calendarApi.getOption('slotMinTime'),
          slotMaxTime: calendarApi.getOption('slotMaxTime'),
          slotDuration: calendarApi.getOption('slotDuration')
        });

        // Force a re-render of the current view
        calendarApi.refetchEvents();
      }
    }, 100);
  };

  const handleServicesChange = (serviceIds: number[]) => {
    setSelectedServices(serviceIds);
    localStorage.setItem('calendar-selected-services', JSON.stringify(serviceIds));
  };

  const handleStatusesChange = (statuses: string[]) => {
    setSelectedStatuses(statuses);
    localStorage.setItem('calendar-selected-statuses', JSON.stringify(statuses));
  };

  const handleNewService = () => {
    openServiceModal();
  };

  const handleServiceSuccess = () => {
    closeServiceModal();
    // Optionally refresh services data or show success message
  };

  // Custom event content renderer
  const renderEventContent = (eventInfo: { event: { extendedProps: any } }) => {
    const {
      appointment,
      isLastInGroup,
      hiddenCount,
      allGroupAppointments
    } = eventInfo.event.extendedProps;

    // If this is the last event in a group and there are hidden appointments, show the "+N" indicator
    if (isLastInGroup && hiddenCount > 0 && allGroupAppointments) {
      return (
        <div className="calendar-event-content relative">
          <CalendarEventCard appointment={appointment} />
          {/* "+N" indicator overlay */}
          <div
            className="absolute top-0 right-0 w-6 h-6 bg-gray-600 hover:bg-gray-700 text-white text-xs font-medium rounded-full flex items-center justify-center cursor-pointer z-10"
            onClick={(e) => {
              e.stopPropagation();
              // Show modal with all appointments
              const rect = e.currentTarget.getBoundingClientRect();
              setModalState({
                visible: true,
                appointments: allGroupAppointments,
                position: {
                  x: rect.left,
                  y: rect.bottom + 5
                }
              });
              setIsModalOpen(true);
            }}
            title={`${hiddenCount} more appointment${hiddenCount > 1 ? 's' : ''}`}
          >
            +{hiddenCount}
          </div>
        </div>
      );
    }

    // Regular single event
    return (
      <div className="calendar-event-content">
        <CalendarEventCard appointment={appointment} />
      </div>
    );
  };

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title="Failed to load appointments"
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      <PageMeta
        title="Appointment Calendar | Provider Dashboard"
        description="Manage your appointments and schedule with an interactive calendar"
      />
      <div className={`flex min-h-[calc(100vh-120px)] rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03] ${isMobile ? 'flex-col' : ''}`}>
        {/* Calendar Sidebar - Hidden on mobile when collapsed */}
        {(!isMobile || !sidebarCollapsed) && (
          <CalendarSidebar
            isCollapsed={sidebarCollapsed}
            onToggle={handleSidebarToggle}
            startTime={calendarConfig.startTime}
            endTime={calendarConfig.endTime}
            timeSlotInterval={calendarConfig.timeSlotInterval}
            onConfigChange={handleConfigChange}
            selectedServices={selectedServices}
            onServicesChange={handleServicesChange}
            onNewService={handleNewService}
            selectedStatuses={selectedStatuses}
            onStatusesChange={handleStatusesChange}
          />
        )}

        {/* Main Calendar Content */}
        <div className="flex-1 flex flex-col">
          {/* Calendar Header */}
          <div className={`${isMobile ? 'p-4' : 'p-6'} border-b border-gray-200 dark:border-gray-700`}>
          <div className={`flex items-center ${isMobile ? 'flex-col space-y-3' : 'justify-between'}`}>
            <div className={`${isMobile ? 'text-center' : ''}`}>
              <div className="flex items-center space-x-2">
                {/* Mobile sidebar toggle button */}
                {isMobile && (
                  <button
                    onClick={handleSidebarToggle}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    title="Toggle filters"
                  >
                    <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                    </svg>
                  </button>
                )}
                <h1 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold text-gray-900 dark:text-white`}>
                  {t('appointmentCalendar', 'Appointment Calendar')}
                </h1>
              </div>
              {!isMobile && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t('subtitle', 'Manage your appointments and schedule')}
                </p>
              )}
            </div>
            {!isMobile && (
              <div className="flex items-center space-x-4">
                {/* Status Legend */}
                <div className="flex items-center space-x-3 text-xs">
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <span className="text-gray-600 dark:text-gray-400">{t('status.pending')}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <span className="text-gray-600 dark:text-gray-400">{t('status.confirmed')}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                    <span className="text-gray-600 dark:text-gray-400">{t('status.completed')}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <span className="text-gray-600 dark:text-gray-400">{t('status.cancelled')}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
          </div>

          <div className={`custom-calendar ${isMobile ? 'p-4' : 'p-6'} flex-1 overflow-auto`}>
          {isLoading ? (
            <div className="flex items-center justify-center h-96">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
            </div>
          ) : (
            <FullCalendar
              ref={calendarRef}
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView={isMobile ? "timeGridDay" : "dayGridMonth"}
              timeZone="local"
              locale={getCalendarLocale()}
              direction={isRTL ? 'rtl' : 'ltr'}
              buttonText={getButtonText()}
              headerToolbar={isMobile ? {
                left: "prev,next",
                center: "title",
                right: "addAppointmentButton",
              } : {
                left: "prev,next addAppointmentButton",
                center: "title",
                right: "dayGridMonth,timeGridWeek,timeGridDay",
              }}
              events={events}
              selectable={true}
              select={handleDateSelect}
              eventClick={handleEventClick}
              eventMouseEnter={handleEventMouseEnter}
              eventMouseLeave={handleEventMouseLeave}
              eventContent={renderEventContent}
              height="auto"
              eventDisplay="block"
              dayMaxEvents={isMobile ? 2 : 3}
              moreLinkClick="popover"
              slotMinTime={calendarConfig.startTime || '08:00'}
              slotMaxTime={calendarConfig.endTime || '18:00'}
              slotDuration={`00:${calendarConfig.timeSlotInterval || 30}:00`}
              slotLabelInterval={`00:${calendarConfig.timeSlotInterval || 30}:00`}
              // 24-hour time format
              slotLabelFormat={{
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              }}
              eventTimeFormat={{
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              }}
              eventMinHeight={isMobile ? 40 : 58}
              eventShortHeight={isMobile ? 40 : 58}
              customButtons={{
                addAppointmentButton: {
                  text: t('newAppointment'),
                  click: handleCreateAppointment,
                },
              }}
              // Force day view on mobile by disabling view switching
              viewClassNames={isMobile ? "mobile-calendar-view" : ""}
              // Additional mobile optimizations
              aspectRatio={isMobile ? 0.8 : 1.35}
              contentHeight={isMobile ? "auto" : undefined}
            />
          )}
          </div>
        </div>

        {/* Appointment Modal */}
        <Modal
          isOpen={isOpen}
          onClose={handleCloseModal}
          className="max-w-[800px] p-0"
        >
          {viewMode === 'create' ? (
            <AppointmentForm
              selectedDate={selectedDate}
              onClose={handleCloseModal}
              onSuccess={handleAppointmentSuccess}
            />
          ) : viewMode === 'view' && selectedAppointment ? (
            <AppointmentDetails
              appointment={selectedAppointment}
              onClose={handleCloseModal}
            />
          ) : (
            <div className="p-6">
              <p className="text-gray-500 dark:text-gray-400">
                No appointment selected
              </p>
            </div>
          )}
        </Modal>

        {/* Service Modal */}
        <Modal
          isOpen={isServiceModalOpen}
          onClose={closeServiceModal}
          className="max-w-[800px] p-0"
        >
          <ServiceForm
            onClose={closeServiceModal}
            onSuccess={handleServiceSuccess}
          />
        </Modal>

        {/* Event Tooltip */}
        {tooltipState.visible && tooltipState.appointment && (
          <div
            className="calendar-tooltip fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl p-4 min-w-[280px] pointer-events-none"
            style={{
              left: Math.min(Math.max(10, tooltipState.position.x - 140), window.innerWidth - 300),
              top: Math.max(10, tooltipState.position.y - 200), // Position above with fallback to 10px from top
              zIndex: 9999,
            }}
          >
            {/* Arrow pointing down to the event */}
            <div
              className="absolute w-4 h-4 bg-white dark:bg-gray-800 border-r border-b border-gray-200 dark:border-gray-700 transform rotate-45"
              style={{
                bottom: '-8px',
                left: '50%',
                marginLeft: '-8px',
              }}
            ></div>
            <div className="space-y-3">
              {/* Customer Info */}
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">
                  {tooltipState.appointment.customer?.firstName} {tooltipState.appointment.customer?.lastName}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {tooltipState.appointment.customer?.email}
                </p>
                {tooltipState.appointment.customer?.phone && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {tooltipState.appointment.customer?.phone}
                  </p>
                )}
              </div>

              {/* Service Info */}
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {tooltipState.appointment.service?.title}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {t('tooltip.duration')}: {tooltipState.appointment.service?.duration} {t('tooltip.minutes')}
                </p>
              </div>

              {/* Time Info */}
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {new Date(tooltipState.appointment.expectedAppointmentStartTime).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                  })}
                  {tooltipState.appointment.expectedAppointmentEndTime && (
                    <span>
                      {' - '}
                      {new Date(tooltipState.appointment.expectedAppointmentEndTime).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                      })}
                    </span>
                  )}
                </p>
              </div>

              {/* Status */}
              <div>
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  tooltipState.appointment.status === 'confirmed'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : tooltipState.appointment.status === 'pending'
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : tooltipState.appointment.status === 'completed'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    : tooltipState.appointment.status === 'InProgress'
                    ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}>
                  {tooltipState.appointment.status === 'InProgress'
                    ? t('status.inProgress')
                    : t(`status.${tooltipState.appointment.status}`) || tooltipState.appointment.status}
                </span>
              </div>

              {/* Click hint */}
              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Click to view details
                </p>
              </div>
            </div>
          </div>
        )}

        {/* "+N" Appointments Modal */}
        {modalState.visible && modalState.appointments.length > 0 && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-[9998]"
              onClick={() => {
                setModalState({ visible: false, appointments: [], position: { x: 0, y: 0 } });
                setIsModalOpen(false);
              }}
            />

            {/* Modal */}
            <div
              className="fixed z-[9999] bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl max-w-sm w-80"
              style={{
                left: Math.min(modalState.position.x, window.innerWidth - 320),
                top: Math.min(modalState.position.y, window.innerHeight - 400),
              }}
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                    {modalState.appointments.length} Appointments
                  </h3>
                  <button
                    onClick={() => {
                      setModalState({ visible: false, appointments: [], position: { x: 0, y: 0 } });
                      setIsModalOpen(false);
                    }}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div className="space-y-2 max-h-80 overflow-y-auto">
                  {modalState.appointments.map((appointment) => (
                    <div
                      key={appointment.id}
                      className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                      onClick={() => {
                        setSelectedAppointment(appointment as any);
                        setSelectedDate(null);
                        setViewMode('view');
                        setModalState({ visible: false, appointments: [], position: { x: 0, y: 0 } });
                        setIsModalOpen(false);
                        openModal();
                      }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {appointment.service?.title || 'Service'}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
                            {appointment.customer?.firstName} {appointment.customer?.lastName}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                            {appointment.service?.duration} min
                          </div>
                        </div>
                        <div className="flex-shrink-0 ml-2">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            appointment.status === 'confirmed'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              : appointment.status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                              : appointment.status === 'completed'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                              : appointment.status === 'InProgress'
                              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                          }`}>
                            {appointment.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </>
        )}

      </div>
    </>
  );
};

export default Calendar;
