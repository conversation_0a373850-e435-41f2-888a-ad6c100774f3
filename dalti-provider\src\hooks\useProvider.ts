import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ProviderService, ProviderLogoResponse } from '../services/provider.service';
import { ProviderProfile, ProviderProfileUpdateRequest } from '../types';
import { ErrorLogger } from '../lib/error-utils';
import { useAuth } from '../context/AuthContext';
import { performS3Upload, UploadOptions } from '../utils/s3-upload.utils';
import toast from 'react-hot-toast';

/**
 * Hook for fetching provider profile
 */
export const useProviderProfile = () => {
  return useQuery<ProviderProfile>({
    queryKey: ['provider', 'profile'],
    queryFn: () => ProviderService.getProfile(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

/**
 * Hook for updating provider profile
 */
export const useUpdateProviderProfile = () => {
  const queryClient = useQueryClient();
  const { updateProvider } = useAuth();

  return useMutation({
    mutationFn: (data: ProviderProfileUpdateRequest) => 
      ProviderService.updateProfile(data),
    onSuccess: (updatedProvider) => {
      // Update the auth context with new provider data
      updateProvider(updatedProvider);
      
      // Invalidate and refetch provider profile
      queryClient.invalidateQueries({ queryKey: ['provider', 'profile'] });
      queryClient.invalidateQueries({ queryKey: ['provider', 'completion'] });
      
      toast.success('Profile updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update profile';
      ErrorLogger.log(error, { context: 'updateProviderProfile' });
      toast.error(message);
    },
  });
};

/**
 * Hook for fetching profile completion status
 */
export const useProfileCompletion = () => {
  return useQuery({
    queryKey: ['provider', 'completion'],
    queryFn: () => ProviderService.getProfileCompletion(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchProfileCompletion' });
    },
  });
};

/**
 * Hook for uploading provider logo using two-phase S3 upload
 */
export const useUploadLogo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (options: Omit<UploadOptions, 'validateFile'>) => {
      return performS3Upload(
        (fileName, fileType) => ProviderService.generateLogoUploadUrl(fileName, fileType),
        { ...options, validateFile: true }
      );
    },
    onSuccess: (result) => {
      // Invalidate provider queries to refetch with new logo
      queryClient.invalidateQueries({ queryKey: ['provider', 'logo'] });
      queryClient.invalidateQueries({ queryKey: ['provider', 'profile'] });
      queryClient.invalidateQueries({ queryKey: ['provider', 'completion'] });

      toast.success('Logo uploaded successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to upload logo';
      ErrorLogger.log(error, { context: 'uploadLogo' });
      toast.error(message);
    },
  });
};

/**
 * Hook for fetching provider logo
 */
export const useGetProviderLogo = () => {
  return useQuery<ProviderLogoResponse>({
    queryKey: ['provider', 'logo'],
    queryFn: () => ProviderService.getLogo(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

/**
 * Hook to check if provider has a logo
 */
export const useHasProviderLogo = () => {
  const { data: logoData, isLoading } = useGetProviderLogo();

  return {
    hasLogo: logoData?.provider?.hasLogo || false,
    logoUrl: logoData?.logo?.downloadUrl || null,
    logoData: logoData?.logo || null,
    isLoading,
  };
};

/**
 * Hook for deleting provider logo
 */
export const useDeleteLogo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => ProviderService.deleteLogo(),
    onSuccess: () => {
      // Invalidate provider logo queries to refetch without logo
      queryClient.invalidateQueries({ queryKey: ['provider', 'logo'] });
      queryClient.invalidateQueries({ queryKey: ['provider', 'profile'] });
      queryClient.invalidateQueries({ queryKey: ['provider', 'completion'] });

      toast.success('Logo removed successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to remove logo';
      ErrorLogger.log(error, { context: 'deleteLogo' });
      toast.error(message);
    },
  });
};
