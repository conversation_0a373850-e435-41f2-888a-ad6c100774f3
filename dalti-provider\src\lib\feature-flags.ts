/**
 * Feature Flags Configuration
 * Controls the rollout and availability of features across the application
 */

/**
 * Feature flag definitions
 */
export interface FeatureFlags {
  // Chargily Pay Integration
  CHARGILY_PAYMENT: boolean;
  CHARGILY_AUTO_SELECTION: boolean;
  CHARGILY_ANALYTICS: boolean;
  CHARGILY_PAYMENT_LINKS: boolean;
  
  // Location Detection
  AUTO_DETECT_LOCATION: boolean;
  BROWSER_GEOLOCATION: boolean;
  IP_GEOLOCATION: boolean;
  
  // UI Features
  ENHANCED_PAYMENT_SELECTOR: boolean;
  PAYMENT_METHOD_RECOMMENDATIONS: boolean;
  CURRENCY_CONVERSION: boolean;
  
  // Testing & Development
  PAYMENT_TEST_MODE: boolean;
  DEBUG_PAYMENT_FLOW: boolean;
  MOCK_LOCATION_DATA: boolean;
  
  // Rollout Control
  ALGERIAN_USERS_ONLY: boolean;
  BETA_USERS_ONLY: boolean;
  GRADUAL_ROLLOUT: boolean;
}

/**
 * Default feature flag values
 */
const DEFAULT_FLAGS: FeatureFlags = {
  // Chargily Pay Integration
  CHARGILY_PAYMENT: true, // Enable by default for testing
  CHARGILY_AUTO_SELECTION: true, // Enable by default for testing
  CHARGILY_ANALYTICS: false,
  CHARGILY_PAYMENT_LINKS: false,

  // Location Detection
  AUTO_DETECT_LOCATION: true, // Enable by default for testing
  BROWSER_GEOLOCATION: true,
  IP_GEOLOCATION: true,

  // UI Features
  ENHANCED_PAYMENT_SELECTOR: true, // Enable by default for testing
  PAYMENT_METHOD_RECOMMENDATIONS: true, // Enable by default for testing
  CURRENCY_CONVERSION: false,

  // Testing & Development
  PAYMENT_TEST_MODE: true, // Enable by default for testing
  DEBUG_PAYMENT_FLOW: true, // Enable by default for testing
  MOCK_LOCATION_DATA: false,

  // Rollout Control
  ALGERIAN_USERS_ONLY: false,
  BETA_USERS_ONLY: false,
  GRADUAL_ROLLOUT: false,
};

/**
 * Environment-based feature flag overrides
 */
const getEnvironmentFlags = (): Partial<FeatureFlags> => {
  const env = import.meta.env.MODE || 'development';
  
  switch (env) {
    case 'development':
      return {
        CHARGILY_PAYMENT: import.meta.env.VITE_ENABLE_CHARGILY === 'true',
        CHARGILY_AUTO_SELECTION: import.meta.env.VITE_ENABLE_CHARGILY_AUTO_SELECTION === 'true',
        AUTO_DETECT_LOCATION: import.meta.env.VITE_AUTO_DETECT_LOCATION === 'true',
        ENHANCED_PAYMENT_SELECTOR: true,
        PAYMENT_METHOD_RECOMMENDATIONS: true,
        PAYMENT_TEST_MODE: true,
        DEBUG_PAYMENT_FLOW: import.meta.env.VITE_DEBUG_PAYMENT === 'true',
        MOCK_LOCATION_DATA: import.meta.env.VITE_MOCK_LOCATION === 'true',
      };
      
    case 'staging':
      return {
        CHARGILY_PAYMENT: true,
        CHARGILY_AUTO_SELECTION: true,
        AUTO_DETECT_LOCATION: true,
        ENHANCED_PAYMENT_SELECTOR: true,
        PAYMENT_METHOD_RECOMMENDATIONS: true,
        PAYMENT_TEST_MODE: true,
        BETA_USERS_ONLY: true,
      };
      
    case 'production':
      return {
        CHARGILY_PAYMENT: import.meta.env.VITE_ENABLE_CHARGILY_PROD === 'true',
        CHARGILY_AUTO_SELECTION: import.meta.env.VITE_ENABLE_CHARGILY_AUTO_SELECTION_PROD === 'true',
        AUTO_DETECT_LOCATION: import.meta.env.VITE_AUTO_DETECT_LOCATION_PROD === 'true',
        ENHANCED_PAYMENT_SELECTOR: true,
        PAYMENT_METHOD_RECOMMENDATIONS: true,
        GRADUAL_ROLLOUT: import.meta.env.VITE_GRADUAL_ROLLOUT === 'true',
        ALGERIAN_USERS_ONLY: import.meta.env.VITE_ALGERIAN_ONLY === 'true',
      };
      
    default:
      return {};
  }
};

/**
 * User-based feature flag overrides
 */
const getUserFlags = (userId?: string, userEmail?: string, userCountry?: string): Partial<FeatureFlags> => {
  const flags: Partial<FeatureFlags> = {};
  
  // Beta users (can be configured via environment or API)
  const betaUsers = (import.meta.env.VITE_BETA_USERS || '').split(',').filter(Boolean);
  if (userEmail && betaUsers.includes(userEmail)) {
    flags.BETA_USERS_ONLY = true;
    flags.CHARGILY_PAYMENT = true;
    flags.CHARGILY_AUTO_SELECTION = true;
  }
  
  // Algerian users
  if (userCountry === 'DZ' || userCountry === 'Algeria') {
    flags.CHARGILY_PAYMENT = true;
    flags.CHARGILY_AUTO_SELECTION = true;
    flags.AUTO_DETECT_LOCATION = true;
  }
  
  // Admin users (full access)
  const adminUsers = (import.meta.env.VITE_ADMIN_USERS || '').split(',').filter(Boolean);
  if (userEmail && adminUsers.includes(userEmail)) {
    Object.keys(DEFAULT_FLAGS).forEach(key => {
      flags[key as keyof FeatureFlags] = true;
    });
  }
  
  return flags;
};

/**
 * Gradual rollout logic
 */
const getGradualRolloutFlags = (userId?: string): Partial<FeatureFlags> => {
  if (!userId) return {};
  
  // Simple hash-based rollout (deterministic)
  const hash = userId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const percentage = hash % 100;
  
  const rolloutPercentage = parseInt(import.meta.env.VITE_ROLLOUT_PERCENTAGE || '0', 10);
  
  if (percentage < rolloutPercentage) {
    return {
      CHARGILY_PAYMENT: true,
      CHARGILY_AUTO_SELECTION: true,
      AUTO_DETECT_LOCATION: true,
    };
  }
  
  return {};
};

/**
 * Feature flag manager class
 */
class FeatureFlagManager {
  private flags: FeatureFlags;
  private listeners: Array<(flags: FeatureFlags) => void> = [];

  constructor() {
    this.flags = { ...DEFAULT_FLAGS };
    this.updateFlags();
  }

  /**
   * Update flags based on environment and user context
   */
  updateFlags(userId?: string, userEmail?: string, userCountry?: string) {
    const environmentFlags = getEnvironmentFlags();
    const userFlags = getUserFlags(userId, userEmail, userCountry);
    const rolloutFlags = this.flags.GRADUAL_ROLLOUT ? getGradualRolloutFlags(userId) : {};

    this.flags = {
      ...DEFAULT_FLAGS,
      ...environmentFlags,
      ...userFlags,
      ...rolloutFlags,
    };

    // Notify listeners
    this.listeners.forEach(listener => listener(this.flags));
  }

  /**
   * Check if a feature is enabled
   */
  isEnabled(flag: keyof FeatureFlags): boolean {
    return this.flags[flag];
  }

  /**
   * Get all flags
   */
  getAllFlags(): FeatureFlags {
    return { ...this.flags };
  }

  /**
   * Override a flag (for testing)
   */
  override(flag: keyof FeatureFlags, value: boolean) {
    this.flags[flag] = value;
    this.listeners.forEach(listener => listener(this.flags));
  }

  /**
   * Subscribe to flag changes
   */
  subscribe(listener: (flags: FeatureFlags) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Get feature flag status for debugging
   */
  getDebugInfo(): {
    environment: string;
    flags: FeatureFlags;
    environmentFlags: Partial<FeatureFlags>;
  } {
    return {
      environment: import.meta.env.MODE || 'development',
      flags: this.flags,
      environmentFlags: getEnvironmentFlags(),
    };
  }
}

// Global feature flag manager instance
export const featureFlags = new FeatureFlagManager();

/**
 * Utility functions for feature flags (non-React)
 */

/**
 * Utility functions
 */
export const isChargilyEnabled = () => featureFlags.isEnabled('CHARGILY_PAYMENT');
export const isAutoLocationEnabled = () => featureFlags.isEnabled('AUTO_DETECT_LOCATION');
export const isTestModeEnabled = () => featureFlags.isEnabled('PAYMENT_TEST_MODE');
export const isDebugModeEnabled = () => featureFlags.isEnabled('DEBUG_PAYMENT_FLOW');

/**
 * Initialize feature flags with user context
 */
export const initializeFeatureFlags = (userId?: string, userEmail?: string, userCountry?: string) => {
  featureFlags.updateFlags(userId, userEmail, userCountry);
};
