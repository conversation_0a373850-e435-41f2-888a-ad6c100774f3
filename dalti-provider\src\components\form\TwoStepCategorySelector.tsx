/**
 * Two-Step Category Selector Component
 * Allows users to first select a parent category, then a child category
 */

import React, { useState, useEffect } from 'react';
import { ProviderCategory } from '../../types';
import { organizeCategories, getCategoryIconForComponent } from '../../utils/category-utils';
import Label from './Label';
import { useAuthTranslation } from '../../hooks/useTranslation';

interface TwoStepCategorySelectorProps {
  categories: ProviderCategory[];
  value?: number;
  onChange: (categoryId: number | undefined) => void;
  disabled?: boolean;
  error?: string;
  required?: boolean;
  placeholder?: string;
}

const TwoStepCategorySelector: React.FC<TwoStepCategorySelectorProps> = ({
  categories,
  value,
  onChange,
  disabled = false,
  error,
  required = false,
  placeholder = "Select your service category",
}) => {
  const { t } = useAuthTranslation();
  const [selectedParentId, setSelectedParentId] = useState<number | undefined>();
  const [selectedChildId, setSelectedChildId] = useState<number | undefined>(value);

  const { parentCategories, childrenByParent } = organizeCategories(categories);

  // Initialize parent selection based on current value
  useEffect(() => {
    if (value && categories.length > 0) {
      const selectedCategory = categories.find(cat => cat.id === value);
      if (selectedCategory) {
        if (selectedCategory.parentId) {
          // It's a child category
          setSelectedParentId(selectedCategory.parentId);
          setSelectedChildId(selectedCategory.id);
        } else {
          // It's a parent category (shouldn't happen in normal flow, but handle it)
          setSelectedParentId(selectedCategory.id);
          setSelectedChildId(undefined);
        }
      }
    }
  }, [value, categories]);

  const handleParentChange = (parentId: string) => {
    const numericParentId = parentId ? parseInt(parentId) : undefined;
    setSelectedParentId(numericParentId);
    setSelectedChildId(undefined);
    onChange(undefined); // Clear selection when parent changes
  };

  const handleChildChange = (childId: string) => {
    const numericChildId = childId ? parseInt(childId) : undefined;
    setSelectedChildId(numericChildId);

    // Only call onChange with valid child category IDs
    // This ensures parent categories cannot be selected as final values
    if (numericChildId && categories.find(c => c.id === numericChildId && c.parentId)) {
      onChange(numericChildId);
    } else {
      onChange(undefined);
    }
  };

  const availableChildren = selectedParentId ? childrenByParent[selectedParentId] || [] : [];

  return (
    <div className="space-y-4">
      {/* Parent Category Selection */}
      <div>
        <Label>
          {t('signUp.serviceCategory')}{required && <span className="text-error-500">*</span>}
        </Label>
        <select
          value={selectedParentId || ''}
          onChange={(e) => handleParentChange(e.target.value)}
          disabled={disabled}
          className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <option value="">{t('signUp.chooseCategoryPlaceholder')}</option>
          {parentCategories.map((category) => (
            <option key={category.id} value={category.id}>
              {getCategoryIconForComponent(category)} {category.title}
            </option>
          ))}
        </select>
      </div>

      {/* Child Category Selection */}
      {selectedParentId && availableChildren.length > 0 && (
        <div className="animate-in slide-in-from-top-2 duration-300">
          <Label>
            {t('signUp.specificService')}{required && <span className="text-error-500">*</span>}
          </Label>
          <select
            value={selectedChildId || ''}
            onChange={(e) => handleChildChange(e.target.value)}
            disabled={disabled}
            className="w-full h-11 rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 shadow-theme-xs focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:focus:border-brand-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <option value="">{t('signUp.chooseSpecificServicePlaceholder')}</option>
            {availableChildren.map((category) => (
              <option key={category.id} value={category.id}>
                {getCategoryIconForComponent(category)} {category.title}
                {category.description && ` - ${category.description}`}
              </option>
            ))}
          </select>
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {t('signUp.servicesAvailable', { count: availableChildren.length })}
          </p>
        </div>
      )}

      {/* Selection Summary */}
      {selectedParentId && selectedChildId && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800 animate-in fade-in duration-300">
          <div className="flex items-center text-sm text-green-800 dark:text-green-200">
            <span className="mr-2">✅</span>
            <span>
              {t('signUp.selected')}: <strong>
                {parentCategories.find(p => p.id === selectedParentId)?.title} → {' '}
                {availableChildren.find(c => c.id === selectedChildId)?.title}
              </strong>
            </span>
          </div>
          {/* Show category description if available */}
          {availableChildren.find(c => c.id === selectedChildId)?.description && (
            <p className="mt-1 text-xs text-green-700 dark:text-green-300">
              {availableChildren.find(c => c.id === selectedChildId)?.description}
            </p>
          )}
        </div>
      )}

      {/* Helper Text */}
      {selectedParentId && availableChildren.length === 0 && (
        <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg dark:bg-amber-900/20 dark:border-amber-800 animate-in fade-in duration-300">
          <div className="flex items-start text-sm text-amber-800 dark:text-amber-200">
            <span className="mr-2 mt-0.5">ℹ️</span>
            <div>
              <div className="font-medium mb-1">{t('signUp.noServicesAvailable')}</div>
              <div className="text-xs">
                {t('signUp.noServicesMessage')}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}

      {/* Instructions */}
      {!selectedParentId && (
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {t('signUp.categoryInstructions')}
        </p>
      )}
    </div>
  );
};

export default TwoStepCategorySelector;
