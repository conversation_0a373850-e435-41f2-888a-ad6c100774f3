/**
 * Subscription guard hooks for protecting actions and features
 */

import { useAuth } from '../context/AuthContext';
import { 
  canCreateQueue, 
  hasSufficientCredits, 
  hasActiveSubscription,
  needsUpgrade,
  getUsageWarnings,
  SubscriptionValidationResult,
  UsageThresholds,
  DEFAULT_THRESHOLDS
} from '../utils/subscription.utils';
import { useNavigate } from 'react-router';
import toast from 'react-hot-toast';

/**
 * Hook for guarding queue creation
 */
export const useQueueCreationGuard = () => {
  const { subscription } = useAuth();
  const navigate = useNavigate();

  const checkCanCreateQueue = (): SubscriptionValidationResult => {
    return canCreateQueue(subscription);
  };

  const guardQueueCreation = (onSuccess: () => void, showToast: boolean = true) => {
    const result = checkCanCreateQueue();
    
    if (result.isValid) {
      onSuccess();
    } else {
      if (showToast) {
        toast.error(result.reason || 'Cannot create queue');
      }
      if (result.upgradeRequired) {
        navigate('/subscription');
      }
    }
    
    return result;
  };

  return {
    canCreateQueue: checkCanCreateQueue,
    guardQueueCreation,
  };
};

/**
 * Hook for guarding credit-based actions
 */
export const useCreditGuard = () => {
  const { subscription } = useAuth();
  const navigate = useNavigate();

  const checkCredits = (requiredCredits: number): SubscriptionValidationResult => {
    return hasSufficientCredits(subscription, requiredCredits);
  };

  const guardCreditAction = (
    requiredCredits: number, 
    onSuccess: () => void, 
    showToast: boolean = true
  ) => {
    const result = checkCredits(requiredCredits);
    
    if (result.isValid) {
      onSuccess();
    } else {
      if (showToast) {
        toast.error(result.reason || 'Insufficient credits');
      }
      if (result.upgradeRequired) {
        navigate('/subscription');
      }
    }
    
    return result;
  };

  return {
    checkCredits,
    guardCreditAction,
  };
};

/**
 * Hook for guarding premium features
 */
export const usePremiumFeatureGuard = () => {
  const { subscription } = useAuth();
  const navigate = useNavigate();

  const checkPremiumAccess = (featureName?: string): SubscriptionValidationResult => {
    const hasAccess = hasActiveSubscription(subscription);
    
    return {
      isValid: hasAccess,
      reason: hasAccess ? undefined : `${featureName || 'This feature'} requires an active subscription`,
      upgradeRequired: !hasAccess,
    };
  };

  const guardPremiumFeature = (
    onSuccess: () => void, 
    featureName?: string,
    showToast: boolean = true
  ) => {
    const result = checkPremiumAccess(featureName);
    
    if (result.isValid) {
      onSuccess();
    } else {
      if (showToast) {
        toast.error(result.reason || 'Premium feature requires subscription');
      }
      navigate('/subscription');
    }
    
    return result;
  };

  return {
    checkPremiumAccess,
    guardPremiumFeature,
  };
};

/**
 * Hook for general subscription validation
 */
export const useSubscriptionValidation = (thresholds: UsageThresholds = DEFAULT_THRESHOLDS) => {
  const { subscription, subscriptionLoading } = useAuth();

  const isLoading = subscriptionLoading;
  const hasSubscription = !!subscription;
  const isActive = hasActiveSubscription(subscription);
  const upgradeNeeded = needsUpgrade(subscription, thresholds);
  const warnings = getUsageWarnings(subscription, thresholds);

  const validateAction = (
    requirements: {
      requiresActiveSubscription?: boolean;
      requiresCredits?: number;
      requiresQueueAvailability?: boolean;
    }
  ): SubscriptionValidationResult => {
    // Check if subscription data is available
    if (!subscription) {
      return {
        isValid: false,
        reason: 'Subscription data not available',
        upgradeRequired: true,
      };
    }

    // Check active subscription requirement
    if (requirements.requiresActiveSubscription && !isActive) {
      return {
        isValid: false,
        reason: 'Active subscription required',
        upgradeRequired: true,
      };
    }

    // Check credit requirements
    if (requirements.requiresCredits) {
      const creditResult = hasSufficientCredits(subscription, requirements.requiresCredits);
      if (!creditResult.isValid) {
        return creditResult;
      }
    }

    // Check queue availability
    if (requirements.requiresQueueAvailability) {
      const queueResult = canCreateQueue(subscription);
      if (!queueResult.isValid) {
        return queueResult;
      }
    }

    return { isValid: true };
  };

  return {
    isLoading,
    hasSubscription,
    isActive,
    upgradeNeeded,
    warnings,
    validateAction,
    subscription,
  };
};

/**
 * Hook for subscription status checks
 */
export const useSubscriptionStatus = () => {
  const { subscription, subscriptionLoading, subscriptionError } = useAuth();

  const status = {
    isLoading: subscriptionLoading,
    hasError: !!subscriptionError,
    error: subscriptionError,
    hasSubscription: !!subscription,
    isActive: hasActiveSubscription(subscription),
    planId: subscription?.subscription?.planId || null,
    planName: subscription?.subscription?.planName || null,
    credits: subscription?.user?.credits || 0,
    queues: subscription?.user?.queues || 0,
    maxCredits: subscription?.subscription?.planDetails?.effect?.amount || 0,
    maxQueues: subscription?.subscription?.planDetails?.effect?.queues || 0,
    hasCustomerPortal: subscription?.hasCustomerPortal || false,
  };

  const usage = {
    creditsUsed: status.maxCredits - status.credits,
    creditsRemaining: status.credits,
    creditsPercentage: status.maxCredits > 0 ? (status.creditsUsed / status.maxCredits) * 100 : 0,
    queuesUsed: status.queues,
    queuesRemaining: status.maxQueues - status.queues,
    queuesPercentage: status.maxQueues > 0 ? (status.queues / status.maxQueues) * 100 : 0,
  };

  const limits = {
    isAtCreditLimit: usage.creditsPercentage >= 95,
    isAtQueueLimit: usage.queuesPercentage >= 95,
    isLowOnCredits: usage.creditsPercentage >= 80,
    isHighQueueUsage: usage.queuesPercentage >= 80,
  };

  return {
    status,
    usage,
    limits,
    subscription,
  };
};

/**
 * Hook for subscription actions with validation
 */
export const useSubscriptionActions = () => {
  const navigate = useNavigate();
  const { refreshSubscriptionData } = useAuth();

  const navigateToUpgrade = (reason?: string) => {
    if (reason) {
      toast.error(reason);
    }
    navigate('/subscription');
  };

  const navigateToBilling = () => {
    navigate('/billing');
  };

  const refreshData = async () => {
    try {
      await refreshSubscriptionData();
      toast.success('Subscription data refreshed');
    } catch (error) {
      toast.error('Failed to refresh subscription data');
    }
  };

  return {
    navigateToUpgrade,
    navigateToBilling,
    refreshData,
  };
};
