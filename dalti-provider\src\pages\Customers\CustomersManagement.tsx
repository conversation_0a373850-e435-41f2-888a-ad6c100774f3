import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { Modal } from "../../components/ui/modal";
import { useModal } from "../../hooks/useModal";
import { ErrorDisplay } from "../../components/error";
import ConfirmationDialog from "../../components/ui/confirmation/ConfirmationDialog";
import { useConfirmation } from "../../hooks/useConfirmation";
import {
  useProviderCustomers,
  useDeactivateCustomerRelationship,
  useSoftDeleteCustomer
} from "../../hooks/useProviderCustomers";
import ProviderCustomerForm from "../../components/customers/ProviderCustomerForm";
import ProviderCustomerCard from "../../components/customers/ProviderCustomerCard";
import ProviderCustomerDetails from "../../components/customers/ProviderCustomerDetails";
import CustomerStatistics from "../../components/customers/CustomerStatistics";
import CustomerExport from "../../components/customers/CustomerExport";
import CustomerQuickActions from "../../components/customers/CustomerQuickActions";
import RestoreCustomerModal from "../../components/customers/RestoreCustomerModal";
import { ProviderCustomer, ProviderCustomerFilters } from "../../types/provider-customer";
import { UserGroupIcon, MagnifyingGlassIcon } from "../../icons";
import { useManagementTranslation, useCommonTranslation } from "../../hooks/useTranslation";

// Separate component for customer results to prevent full page re-render
interface CustomerResultsProps {
  filters: ProviderCustomerFilters;
  viewMode: 'cards' | 'table';
  onViewCustomer: (customer: ProviderCustomer) => void;
  onEditCustomer: (customer: ProviderCustomer) => void;
  onDeleteCustomer: (customer: ProviderCustomer) => void;
  onCreateCustomer: () => void;
  onFilterChange: (newFilters: Partial<ProviderCustomerFilters>) => void;
  deactivateCustomerMutation: any;
  hasActiveFilters: boolean;
}

const CustomerResults: React.FC<CustomerResultsProps> = React.memo(({
  filters,
  viewMode,
  onViewCustomer,
  onEditCustomer,
  onDeleteCustomer,
  onCreateCustomer,
  onFilterChange,
  deactivateCustomerMutation,
  hasActiveFilters
}) => {
  const { data: customersResponse, isLoading, error } = useProviderCustomers(filters);
  const { t } = useManagementTranslation();

  // Use standard translation keys from management.json

  // Ensure customers is always an array
  const customers = Array.isArray(customersResponse?.customers) ? customersResponse.customers : [];
  const pagination = customersResponse?.pagination;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <ErrorDisplay
          error={error}
          title={t('customers.failedToLoad')}
          variant="card"
          showRetry
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <>
      {/* Customers List */}
      {customers && customers.length > 0 ? (
        <div className={viewMode === 'cards' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {customers.map((customer) => (
            <ProviderCustomerCard
              key={customer.id}
              customer={customer}
              viewMode={viewMode}
              onView={() => onViewCustomer(customer)}
              onEdit={() => onEditCustomer(customer)}
              onDelete={() => onDeleteCustomer(customer)}
              isDeleting={deactivateCustomerMutation.isPending}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
            <UserGroupIcon className="w-full h-full" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('customers.noCustomersFound')}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            {hasActiveFilters
              ? t('customers.noCustomersFiltered')
              : t('customers.getStarted')
            }
          </p>
          {!hasActiveFilters && (
            <Button onClick={onCreateCustomer}>
              {t('customers.addFirstCustomer')}
            </Button>
          )}
        </div>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            {t('pagination.showing', 'Showing')} {((pagination.page - 1) * pagination.limit) + 1} {t('pagination.to', 'to')} {Math.min(pagination.page * pagination.limit, pagination.total)} {t('pagination.of', 'of')} {pagination.total} {tCommon('navigation.customers')}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => onFilterChange({ page: pagination.page - 1 })}
              disabled={!pagination.hasPrev}
              variant="outline"
              size="sm"
            >
              {tCommon('actions.previous')}
            </Button>
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {t('pagination.page', 'Page')} {pagination.page} {t('pagination.of', 'of')} {pagination.totalPages}
            </span>
            <Button
              onClick={() => onFilterChange({ page: pagination.page + 1 })}
              disabled={!pagination.hasNext}
              variant="outline"
              size="sm"
            >
              {tCommon('actions.next')}
            </Button>
          </div>
        </div>
      )}
    </>
  );
});

export default function CustomersManagement() {
  const [editingCustomer, setEditingCustomer] = useState<ProviderCustomer | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<ProviderCustomer | null>(null);
  const [filters, setFilters] = useState<ProviderCustomerFilters>({});
  const [modalType, setModalType] = useState<'form' | 'details' | 'export' | 'restore' | null>(null);
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');
  const [showStatistics, setShowStatistics] = useState(true);
  const { isOpen, openModal, closeModal } = useModal();
  const { t } = useManagementTranslation();
  const { t: tCommon } = useCommonTranslation();

  // Remove useProviderCustomers hook from here - it's now in CustomerResults component
  const deactivateCustomerMutation = useDeactivateCustomerRelationship();
  const softDeleteCustomerMutation = useSoftDeleteCustomer();
  const confirmation = useConfirmation();

  // We need to fetch customers for statistics - we'll use a separate hook call for this
  const { data: customersResponse } = useProviderCustomers({});
  const customers = Array.isArray(customersResponse?.customers) ? customersResponse.customers : [];

  const handleCreateCustomer = () => {
    setEditingCustomer(null);
    setModalType('form');
    openModal();
  };

  const handleEditCustomer = (customer: ProviderCustomer) => {
    setEditingCustomer(customer);
    setModalType('form');
    openModal();
  };

  const handleViewCustomer = (customer: ProviderCustomer) => {
    setSelectedCustomer(customer);
    setModalType('details');
    openModal();
  };

  const handleDeleteCustomer = async (customer: ProviderCustomer) => {
    const confirmed = await confirmation.confirm({
      title: 'Delete Customer',
      message: `Are you sure you want to delete ${customer.firstName} ${customer.lastName}? This action will move the customer to archive and can be restored later.`,
      confirmText: 'Delete Customer',
      cancelText: 'Cancel',
      variant: 'danger'
    });

    if (confirmed) {
      try {
        await softDeleteCustomerMutation.mutateAsync(customer.id);
        confirmation.close();
      } catch (error) {
        confirmation.close();
        // Error handled by mutation
      }
    }
  };

  const handleExportCustomers = () => {
    setModalType('export');
    openModal();
  };

  const handleRestoreCustomer = () => {
    setModalType('restore');
    openModal();
  };

  const handleCloseModal = () => {
    setEditingCustomer(null);
    setSelectedCustomer(null);
    setModalType(null);
    closeModal();
  };

  const handleSuccess = () => {
    handleCloseModal();
  };

  const handleFilterChange = (newFilters: Partial<ProviderCustomerFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  const hasActiveFilters = Object.keys(filters).some(key =>
    filters[key as keyof ProviderCustomerFilters] !== undefined &&
    filters[key as keyof ProviderCustomerFilters] !== ''
  );

  return (
    <>
      <PageMeta
        title="Customer Management | Provider Dashboard"
        description="Manage your customer relationships, track interactions, and maintain customer records"
      />
      <PageBreadcrumb pageTitle={tCommon('navigation.customers')} />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('customers.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('customers.description')}
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleCreateCustomer}
              size="sm"
            >
              {t('customers.addNewCustomer')}
            </Button>
            <Button
              onClick={handleRestoreCustomer}
              variant="outline"
              size="sm"
            >
              {t('customers.restore')}
            </Button>
          </div>
        </div>

        {/* Customer Statistics */}
        {showStatistics && customers.length > 0 && (
          <CustomerStatistics customers={customers} />
        )}

        {/* Quick Actions and Filters */}
        <CustomerQuickActions
          customers={customers}
          onCreateCustomer={handleCreateCustomer}
          onExportCustomers={handleExportCustomers}
          onFilterChange={handleFilterChange}
          currentFilters={filters}
        />

        {/* View Mode Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{tCommon('actions.view')}:</span>
            <button
              onClick={() => setViewMode('cards')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'cards'
                  ? 'bg-brand-100 text-brand-600 dark:bg-brand-900/20 dark:text-brand-400'
                  : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode('table')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'table'
                  ? 'bg-brand-100 text-brand-600 dark:bg-brand-900/20 dark:text-brand-400'
                  : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
              }`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowStatistics(!showStatistics)}
              className="text-sm text-brand-600 dark:text-brand-400 hover:text-brand-700 dark:hover:text-brand-300"
            >
              {showStatistics ? tCommon('actions.hide', 'Hide') : tCommon('actions.show', 'Show')} {t('customers.statistics', 'Statistics')}
            </button>
          </div>
        </div>

        {/* Customers List - Now handled by CustomerResults component */}
        <CustomerResults
          filters={filters}
          viewMode={viewMode}
          onViewCustomer={handleViewCustomer}
          onEditCustomer={handleEditCustomer}
          onDeleteCustomer={handleDeleteCustomer}
          onCreateCustomer={handleCreateCustomer}
          onFilterChange={handleFilterChange}
          deactivateCustomerMutation={deactivateCustomerMutation}
          hasActiveFilters={hasActiveFilters}
        />
      </div>

      {/* Customer Form Modal */}
      <Modal
        isOpen={isOpen && modalType === 'form'}
        onClose={handleCloseModal}
        className="max-w-[800px] p-0"
        showCloseButton={false}
      >
        <ProviderCustomerForm
          customer={editingCustomer}
          onClose={handleCloseModal}
          onSuccess={handleSuccess}
        />
      </Modal>

      {/* Customer Details Modal */}
      <Modal
        isOpen={isOpen && modalType === 'details'}
        onClose={handleCloseModal}
        className="max-w-[1000px] p-0"
        showCloseButton={false}
      >
        {selectedCustomer && (
          <ProviderCustomerDetails
            customer={selectedCustomer}
            onClose={handleCloseModal}
            onEdit={() => {
              setEditingCustomer(selectedCustomer);
              setSelectedCustomer(null);
              setModalType('form');
            }}
          />
        )}
      </Modal>

      {/* Customer Export Modal */}
      <CustomerExport
        customers={customers}
        isOpen={isOpen && modalType === 'export'}
        onClose={handleCloseModal}
      />

      {/* Restore Customer Modal */}
      <RestoreCustomerModal
        isOpen={isOpen && modalType === 'restore'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmation.isOpen}
        onClose={confirmation.cancel}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        variant={confirmation.variant}
        isLoading={confirmation.isLoading}
      />
    </>
  );
}
