import React, { createContext, useContext, useEffect, useState } from 'react';
import { SupportedCurrency, formatCurrency, getCurrencySymbol } from '../utils/currency.utils';
import { useAuth } from './AuthContext';
import { ErrorLogger } from '../lib/error-utils';

// Types
export interface CurrencyContextType {
  defaultCurrency: SupportedCurrency;
  currencySymbol: string;
  setDefaultCurrency: (currency: SupportedCurrency) => void;
  formatPrice: (amount: number, options?: {
    showSymbol?: boolean;
    showCode?: boolean;
    compact?: boolean;
  }) => string;
  isLoading: boolean;
}

// Create context
const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

// Currency Provider Props
interface CurrencyProviderProps {
  children: React.ReactNode;
}

// Default currency based on user location/preferences
const getDefaultCurrency = (): SupportedCurrency => {
  // Check localStorage first
  const stored = localStorage.getItem('dalti-currency') as SupportedCurrency;
  if (stored && ['DZD', 'EUR'].includes(stored)) {
    return stored;
  }

  // Try to detect from browser locale
  const locale = navigator.language;
  if (locale.includes('dz') || locale.includes('ar-DZ') || locale.includes('ar')) {
    return 'DZD';
  }
  if (locale.includes('fr') || locale.includes('eu')) {
    return 'EUR';
  }

  // Default to DZD (since this is primarily for Algerian market)
  return 'DZD';
};

// Currency Provider Component
export const CurrencyProvider: React.FC<CurrencyProviderProps> = ({ children }) => {
  const [defaultCurrency, setDefaultCurrencyState] = useState<SupportedCurrency>(getDefaultCurrency);
  const [isLoading, setIsLoading] = useState(false);
  const { user, provider } = useAuth();

  // Get currency symbol for current currency
  const currencySymbol = getCurrencySymbol(defaultCurrency);

  // Format price using current default currency
  const formatPrice = (
    amount: number, 
    options?: {
      showSymbol?: boolean;
      showCode?: boolean;
      compact?: boolean;
    }
  ): string => {
    return formatCurrency(amount, defaultCurrency, options);
  };

  // Set default currency and persist to localStorage
  const setDefaultCurrency = async (currency: SupportedCurrency) => {
    if (currency === defaultCurrency) return;

    setIsLoading(true);
    
    try {
      // Update state immediately for UI responsiveness
      setDefaultCurrencyState(currency);
      
      // Store in localStorage
      localStorage.setItem('dalti-currency', currency);
      
      // TODO: In the future, sync with backend user preferences
      // if (user) {
      //   await UserService.updateCurrencyPreference(currency);
      // }

      console.log(`💰 Currency changed to ${currency} (${getCurrencySymbol(currency)})`);
      
      // Dispatch custom event for other components to listen to
      window.dispatchEvent(new CustomEvent('currencyChanged', {
        detail: { currency, symbol: getCurrencySymbol(currency) }
      }));

    } catch (error) {
      console.error('Failed to change currency:', error);
      ErrorLogger.log(error as Error, { context: 'changeCurrency', currency });
      
      // Revert on error
      setDefaultCurrencyState(defaultCurrency);
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize currency on mount and when user changes
  useEffect(() => {
    const initializeCurrency = () => {
      // If user has a preferred currency in their profile, use that
      // TODO: Implement when backend supports currency preferences
      // if (user?.preferredCurrency) {
      //   setDefaultCurrencyState(user.preferredCurrency as SupportedCurrency);
      //   return;
      // }

      // If provider has a business location, try to infer currency
      if (provider?.businessAddress) {
        // Simple country-based currency detection
        const address = provider.businessAddress.toLowerCase();
        if (address.includes('algeria') || address.includes('dz')) {
          setDefaultCurrencyState('DZD');
          return;
        }
        if (address.includes('france') || address.includes('fr')) {
          setDefaultCurrencyState('EUR');
          return;
        }
        if (address.includes('canada') || address.includes('ca')) {
          setDefaultCurrencyState('CAD');
          return;
        }
      }

      // Otherwise, keep the detected/stored currency
    };

    initializeCurrency();
  }, [user, provider]);

  // Context value
  const contextValue: CurrencyContextType = {
    defaultCurrency,
    currencySymbol,
    setDefaultCurrency,
    formatPrice,
    isLoading,
  };

  return (
    <CurrencyContext.Provider value={contextValue}>
      {children}
    </CurrencyContext.Provider>
  );
};

// Custom hook to use currency context
export const useCurrency = (): CurrencyContextType => {
  const context = useContext(CurrencyContext);
  
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  
  return context;
};

// Utility hook for currency change listeners
export const useCurrencyChangeListener = (
  callback: (currency: SupportedCurrency, symbol: string) => void
) => {
  const { defaultCurrency } = useCurrency();

  useEffect(() => {
    const handleCurrencyChange = (event: CustomEvent) => {
      const { currency, symbol } = event.detail;
      callback(currency, symbol);
    };

    window.addEventListener('currencyChanged', handleCurrencyChange as EventListener);

    return () => {
      window.removeEventListener('currencyChanged', handleCurrencyChange as EventListener);
    };
  }, [callback]);

  return {
    defaultCurrency,
  };
};

export default CurrencyContext;
