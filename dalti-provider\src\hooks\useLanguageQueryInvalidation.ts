import { useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useLanguage } from '../context/LanguageContext';
import { LanguageCode } from '../context/LanguageContext';

/**
 * Hook for handling query invalidation when language changes
 * This provides more granular control over which queries to invalidate
 */
export const useLanguageQueryInvalidation = () => {
  const queryClient = useQueryClient();
  const { currentLanguage } = useLanguage();

  /**
   * Invalidate all queries
   */
  const invalidateAllQueries = useCallback(() => {
    console.log('🔄 Invalidating all queries due to language change');
    queryClient.invalidateQueries();
  }, [queryClient]);

  /**
   * Invalidate specific query patterns
   */
  const invalidateQueriesByPattern = useCallback((patterns: string[]) => {
    console.log('🔄 Invalidating specific queries due to language change:', patterns);
    patterns.forEach(pattern => {
      queryClient.invalidateQueries({ queryKey: [pattern] });
    });
  }, [queryClient]);

  /**
   * Invalidate queries that are likely to contain translatable content
   */
  const invalidateTranslatableQueries = useCallback(() => {
    console.log('🔄 Invalidating translatable queries due to language change');
    
    // Common query patterns that likely contain translatable content
    const translatablePatterns = [
      'services',
      'service-categories', 
      'locations',
      'queues',
      'appointments',
      'notifications',
      'dashboard',
      'provider',
      'crm',
      'subscription'
    ];

    translatablePatterns.forEach(pattern => {
      queryClient.invalidateQueries({ queryKey: [pattern] });
    });
  }, [queryClient]);

  /**
   * Clear all query cache (more aggressive than invalidation)
   */
  const clearAllQueries = useCallback(() => {
    console.log('🗑️ Clearing all query cache due to language change');
    queryClient.clear();
  }, [queryClient]);

  /**
   * Refetch all active queries immediately
   */
  const refetchAllQueries = useCallback(() => {
    console.log('🔄 Refetching all active queries due to language change');
    queryClient.refetchQueries();
  }, [queryClient]);

  return {
    invalidateAllQueries,
    invalidateQueriesByPattern,
    invalidateTranslatableQueries,
    clearAllQueries,
    refetchAllQueries,
    currentLanguage,
  };
};

/**
 * Hook that automatically invalidates queries when language changes
 * Use this in components that need automatic query invalidation on language change
 */
export const useAutoLanguageInvalidation = (
  options: {
    invalidateAll?: boolean;
    invalidatePatterns?: string[];
    clearCache?: boolean;
    refetchActive?: boolean;
  } = {}
) => {
  const {
    invalidateAllQueries,
    invalidateQueriesByPattern,
    clearAllQueries,
    refetchAllQueries,
    currentLanguage,
  } = useLanguageQueryInvalidation();

  const {
    invalidateAll = false,
    invalidatePatterns = [],
    clearCache = false,
    refetchActive = false,
  } = options;

  useEffect(() => {
    const handleLanguageChange = (event: CustomEvent) => {
      const { language } = event.detail;
      console.log('🌐 Language changed, handling query invalidation:', language);

      if (clearCache) {
        clearAllQueries();
      } else if (invalidateAll) {
        invalidateAllQueries();
      } else if (invalidatePatterns.length > 0) {
        invalidateQueriesByPattern(invalidatePatterns);
      }

      if (refetchActive) {
        // Small delay to ensure invalidation is processed first
        setTimeout(() => {
          refetchAllQueries();
        }, 100);
      }
    };

    // Listen to the custom language change event
    window.addEventListener('languageChanged', handleLanguageChange as EventListener);

    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange as EventListener);
    };
  }, [
    invalidateAll,
    invalidatePatterns,
    clearCache,
    refetchActive,
    invalidateAllQueries,
    invalidateQueriesByPattern,
    clearAllQueries,
    refetchAllQueries,
  ]);

  return {
    currentLanguage,
  };
};

/**
 * Hook for components that need to perform custom actions on language change
 */
export const useLanguageChangeListener = (
  callback: (language: LanguageCode, direction: 'ltr' | 'rtl') => void
) => {
  const { currentLanguage } = useLanguage();

  useEffect(() => {
    const handleLanguageChange = (event: CustomEvent) => {
      const { language, direction } = event.detail;
      callback(language, direction);
    };

    window.addEventListener('languageChanged', handleLanguageChange as EventListener);

    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange as EventListener);
    };
  }, [callback]);

  return {
    currentLanguage,
  };
};
